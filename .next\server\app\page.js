/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/AnimatedBackground.tsx */ \"(ssr)/./app/components/AnimatedBackground.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ThemeProvider.tsx */ \"(ssr)/./app/components/ThemeProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDMjAwMjE3OTIlNUNEb2N1bWVudHMlNUNQcm9qZWN0cyU1Q0NoYXQlNUNhcHAlNUNjb21wb25lbnRzJTVDQW5pbWF0ZWRCYWNrZ3JvdW5kLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1QzIwMDIxNzkyJTVDRG9jdW1lbnRzJTVDUHJvamVjdHMlNUNDaGF0JTVDYXBwJTVDY29tcG9uZW50cyU1Q1RoZW1lUHJvdmlkZXIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBNEg7QUFDNUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbGxhbWEtbWNwLWNoYXQvP2ZhOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxBbmltYXRlZEJhY2tncm91bmQudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxUaGVtZVByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Cpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Cpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDMjAwMjE3OTIlNUNEb2N1bWVudHMlNUNQcm9qZWN0cyU1Q0NoYXQlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbGxhbWEtbWNwLWNoYXQvPzU4NjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/AnimatedBackground.tsx":
/*!***********************************************!*\
  !*** ./app/components/AnimatedBackground.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimatedBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AnimatedBackground() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        sx: {\n            position: \"fixed\",\n            top: 0,\n            left: 0,\n            width: \"100%\",\n            height: \"100%\",\n            zIndex: -1,\n            overflow: \"hidden\",\n            background: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    top: \"10%\",\n                    left: \"10%\",\n                    width: \"300px\",\n                    height: \"300px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\",\n                    filter: \"blur(40px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        100,\n                        0\n                    ],\n                    y: [\n                        0,\n                        -50,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.2,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    top: \"60%\",\n                    right: \"10%\",\n                    width: \"400px\",\n                    height: \"400px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%)\",\n                    filter: \"blur(40px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        -80,\n                        0\n                    ],\n                    y: [\n                        0,\n                        60,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        0.8,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 25,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    bottom: \"20%\",\n                    left: \"30%\",\n                    width: \"250px\",\n                    height: \"250px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%)\",\n                    filter: \"blur(30px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        60,\n                        0\n                    ],\n                    y: [\n                        0,\n                        -40,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.3,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 18,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            Array.from({\n                length: 20\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    style: {\n                        position: \"absolute\",\n                        width: \"4px\",\n                        height: \"4px\",\n                        borderRadius: \"50%\",\n                        background: \"rgba(255, 255, 255, 0.2)\",\n                        left: `${Math.random() * 100}%`,\n                        top: `${Math.random() * 100}%`\n                    },\n                    animate: {\n                        y: [\n                            0,\n                            -100,\n                            0\n                        ],\n                        opacity: [\n                            0,\n                            1,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 3 + Math.random() * 2,\n                        repeat: Infinity,\n                        delay: Math.random() * 2,\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/AnimatedBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(ssr)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(ssr)/./app/components/ChatWindow/InputBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ChatWindow({ selectedModel, availableTools, conversation, onMessageSent, modelCapabilities }) {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messages = conversation?.messages || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError(\"Please select a model first\");\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n            role: \"user\",\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments?.some((att)=>att.type === \"image\");\n            // Validate vision capability\n            if (hasVisionContent && !modelCapabilities?.supportsVision) {\n                setError(`Model ${selectedModel} does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.`);\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = modelCapabilities?.supportsTools ? availableTools : [];\n            // Send to Ollama API\n            const response = await fetch(\"/api/ollama\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response from Ollama\");\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n                role: \"assistant\",\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if (modelCapabilities?.supportsTools && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                const toolCall = assistantMessage.tool_calls[0];\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error(\"Error sending message:\", err);\n            setError(\"Failed to send message. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            // Parse tool arguments\n            const toolArgs = JSON.parse(toolCall.function.arguments);\n            // Execute tool via MCP API\n            const response = await fetch(\"/api/mcp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"execute_tool\",\n                    serverName: \"exa\",\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to execute tool\");\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n                role: \"tool\",\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch(\"/api/ollama\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n                    role: \"assistant\",\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error(\"Error executing tool call:\", err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n                role: \"assistant\",\n                content: \"Sorry, I encountered an error while using the tool. Please try again.\",\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    p: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    severity: \"error\",\n                    onClose: ()=>setError(null),\n                    sx: {\n                        borderRadius: 2,\n                        backdropFilter: \"blur(20px)\",\n                        background: \"rgba(255, 255, 255, 0.05)\",\n                        border: \"1px solid rgba(255, 255, 255, 0.1)\"\n                    },\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        height: \"100%\",\n                        textAlign: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                variant: \"h6\",\n                                color: \"text.secondary\",\n                                gutterBottom: true,\n                                children: conversation ? \"Start chatting\" : \"Select a conversation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"text.secondary\",\n                                children: conversation ? \"Type a message to begin the conversation\" : \"Choose a conversation from the sidebar or create a new one\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: message,\n                            isUser: message.role === \"user\"\n                        }, message.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    p: 3,\n                    borderTop: \"1px solid rgba(255, 255, 255, 0.1)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ChatWindow/ChatWindow.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ChatWindow/InputBar.tsx":
/*!************************************************!*\
  !*** ./app/components/ChatWindow/InputBar.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InputBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Chip,CircularProgress,IconButton,TextField,Tooltip!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Image.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/AttachFile.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AttachFile,Close,Image,Send!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction InputBar({ onSendMessage, disabled, loading, modelCapabilities }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSend = ()=>{\n        if (message.trim() || attachments.length > 0) {\n            onSendMessage(message.trim(), attachments);\n            setMessage(\"\");\n            setAttachments([]);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    const handleFileSelect = async (e)=>{\n        const files = Array.from(e.target.files || []);\n        if (files.length === 0) return;\n        // Check for image files and vision capability\n        const imageFiles = files.filter((file)=>file.type.startsWith(\"image/\"));\n        if (imageFiles.length > 0 && !modelCapabilities?.supportsVision) {\n            alert(\"The selected model does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl to upload images.\");\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n            return;\n        }\n        setUploading(true);\n        try {\n            const uploadPromises = files.map(async (file)=>{\n                const formData = new FormData();\n                formData.append(\"file\", file);\n                const response = await fetch(\"/api/upload\", {\n                    method: \"POST\",\n                    body: formData\n                });\n                if (!response.ok) {\n                    throw new Error(`Failed to upload ${file.name}`);\n                }\n                const data = await response.json();\n                return data.file;\n            });\n            const uploadedFiles = await Promise.all(uploadPromises);\n            setAttachments((prev)=>[\n                    ...prev,\n                    ...uploadedFiles\n                ]);\n        } catch (error) {\n            console.error(\"Error uploading files:\", error);\n        // TODO: Show error toast\n        } finally{\n            setUploading(false);\n            // Reset file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n        }\n    };\n    const removeAttachment = (attachmentId)=>{\n        setAttachments((prev)=>prev.filter((att)=>att.id !== attachmentId));\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                mb: 2,\n                display: \"flex\",\n                flexWrap: \"wrap\",\n                gap: 1,\n                children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        icon: attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 51\n                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 67\n                        }, void 0),\n                        label: attachment.name,\n                        onDelete: ()=>removeAttachment(attachment.id),\n                        deleteIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 27\n                        }, void 0),\n                        size: \"small\",\n                        sx: {\n                            backdropFilter: \"blur(20px)\",\n                            background: \"rgba(255, 255, 255, 0.1)\",\n                            border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                            color: \"white\"\n                        }\n                    }, attachment.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                lineNumber: 114,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                display: \"flex\",\n                alignItems: \"flex-end\",\n                gap: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        title: !modelCapabilities?.supportsVision ? \"Image uploads require a vision-capable model\" : \"Attach files\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            onClick: ()=>fileInputRef.current?.click(),\n                            disabled: disabled || uploading,\n                            sx: {\n                                backdropFilter: \"blur(20px)\",\n                                background: \"rgba(255, 255, 255, 0.05)\",\n                                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                                color: \"white\",\n                                \"&:hover\": {\n                                    background: \"rgba(255, 255, 255, 0.1)\"\n                                }\n                            },\n                            children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: modelCapabilities?.supportsVision ? \"image/*,.pdf,.txt,.md,.doc,.docx\" : \".pdf,.txt,.md,.doc,.docx\",\n                        onChange: handleFileSelect,\n                        style: {\n                            display: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        fullWidth: true,\n                        multiline: true,\n                        maxRows: 4,\n                        value: message,\n                        onChange: (e)=>setMessage(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: \"Type your message...\",\n                        variant: \"outlined\",\n                        disabled: disabled\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"contained\",\n                        onClick: handleSend,\n                        disabled: disabled || loading || !message.trim() && attachments.length === 0,\n                        sx: {\n                            minWidth: 48,\n                            height: 48,\n                            borderRadius: 3\n                        },\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Chip_CircularProgress_IconButton_TextField_Tooltip_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            size: 20,\n                            color: \"inherit\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AttachFile_Close_Image_Send_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\InputBar.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ChatWindow/InputBar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ChatWindow/MessageBubble.tsx":
/*!*****************************************************!*\
  !*** ./app/components/ChatWindow/MessageBubble.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Build,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Image.js\");\n/* harmony import */ var _barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Build,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Build,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Build,ContentCopy,Description,Image!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/ContentCopy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MessageBubble({ message, isUser }) {\n    const handleCopyMessage = ()=>{\n        navigator.clipboard.writeText(message.content);\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Intl.DateTimeFormat(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        }).format(new Date(timestamp));\n    };\n    const renderAttachments = ()=>{\n        if (!message.attachments || message.attachments.length === 0) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            display: \"flex\",\n            flexWrap: \"wrap\",\n            gap: 1,\n            mb: 1,\n            children: message.attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: attachment.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 49\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 65\n                    }, void 0),\n                    label: attachment.name,\n                    size: \"small\",\n                    variant: \"outlined\",\n                    onClick: ()=>window.open(attachment.url, \"_blank\"),\n                    sx: {\n                        cursor: \"pointer\"\n                    }\n                }, attachment.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolCall = ()=>{\n        if (!message.toolCall) {\n            return null;\n        }\n        const toolArgs = JSON.parse(message.toolCall.function.arguments);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 17\n                    }, void 0),\n                    label: `Tool: ${message.toolCall.function.name}`,\n                    size: \"small\",\n                    color: \"secondary\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"grey.100\",\n                    borderRadius: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"caption\",\n                            color: \"text.secondary\",\n                            children: \"Arguments:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"body2\",\n                            component: \"pre\",\n                            sx: {\n                                fontSize: \"0.75rem\"\n                            },\n                            children: JSON.stringify(toolArgs, null, 2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolResult = ()=>{\n        if (!message.toolResult) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 17\n                    }, void 0),\n                    label: \"Tool Result\",\n                    size: \"small\",\n                    color: \"success\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"success.50\",\n                    borderRadius: 1,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        component: \"pre\",\n                        sx: {\n                            fontSize: \"0.875rem\"\n                        },\n                        children: typeof message.toolResult.result === \"string\" ? message.toolResult.result : JSON.stringify(message.toolResult.result, null, 2)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        display: \"flex\",\n        justifyContent: isUser ? \"flex-end\" : \"flex-start\",\n        mb: 3,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            sx: {\n                maxWidth: \"80%\",\n                p: 2,\n                borderRadius: 3,\n                backdropFilter: \"blur(20px)\",\n                background: isUser ? \"rgba(255, 255, 255, 0.1)\" : \"rgba(255, 255, 255, 0.05)\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                position: \"relative\",\n                \"&:hover .copy-button\": {\n                    opacity: 1\n                }\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"copy-button\",\n                    size: \"small\",\n                    onClick: handleCopyMessage,\n                    sx: {\n                        position: \"absolute\",\n                        top: 8,\n                        right: 8,\n                        opacity: 0,\n                        transition: \"opacity 0.2s\",\n                        color: \"rgba(255, 255, 255, 0.7)\",\n                        \"&:hover\": {\n                            background: \"rgba(255, 255, 255, 0.1)\"\n                        }\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        fontSize: \"small\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                renderAttachments(),\n                renderToolCall(),\n                renderToolResult(),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"body1\",\n                    sx: {\n                        whiteSpace: \"pre-wrap\",\n                        wordBreak: \"break-word\",\n                        pr: 5,\n                        color: \"white\"\n                    },\n                    children: message.content\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    variant: \"caption\",\n                    sx: {\n                        display: \"block\",\n                        mt: 1,\n                        color: \"rgba(255, 255, 255, 0.5)\",\n                        textAlign: isUser ? \"right\" : \"left\"\n                    },\n                    children: formatTimestamp(message.timestamp)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ChatWindow/MessageBubble.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ModelSelector.tsx":
/*!******************************************!*\
  !*** ./app/components/ModelSelector.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Code.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ModelSelector({ selectedModel, onModelSelect, onModelCapabilitiesChange }) {\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchModels();\n    }, []);\n    const fetchModels = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/ollama\");\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch models\");\n            }\n            const data = await response.json();\n            setModels(data.models || []);\n            // Auto-select first model if none selected\n            if (!selectedModel && data.models && data.models.length > 0) {\n                const firstModel = data.models[0];\n                onModelSelect(firstModel.name);\n                onModelCapabilitiesChange?.(firstModel.capabilities);\n            }\n        } catch (err) {\n            console.error(\"Error fetching models:\", err);\n            setError(\"Failed to load Ollama models. Make sure Ollama is running.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatModelSize = (size)=>{\n        const gb = size / (1024 * 1024 * 1024);\n        return `${gb.toFixed(1)}GB`;\n    };\n    const getModelFamily = (model)=>{\n        if (model.details?.family) {\n            return model.details.family;\n        }\n        return model.name.split(\":\")[0];\n    };\n    const getCapabilityIcon = (capability)=>{\n        switch(capability){\n            case \"tools\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 28\n                }, this);\n            case \"vision\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 29\n                }, this);\n            case \"code\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 27\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getCapabilityColor = (capability)=>{\n        switch(capability){\n            case \"tools\":\n                return \"primary\";\n            case \"vision\":\n                return \"secondary\";\n            case \"code\":\n                return \"success\";\n            default:\n                return \"default\";\n        }\n    };\n    const handleModelChange = (modelName)=>{\n        const model = models.find((m)=>m.name === modelName);\n        onModelSelect(modelName);\n        if (model) {\n            onModelCapabilitiesChange?.(model.capabilities);\n        }\n    };\n    const selectedModelData = models.find((m)=>m.name === selectedModel);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    p: 3,\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 40,\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Discovering Models\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Connecting to Ollama and loading available models...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    borderRadius: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    if (models.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                severity: \"warning\",\n                sx: {\n                    borderRadius: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"No Models Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    \"No Ollama models found. Install a model using: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: \"ollama pull llama3.2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 58\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            fullWidth: true,\n            size: \"small\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    sx: {\n                        color: \"rgba(255, 255, 255, 0.7)\"\n                    },\n                    children: \"Model\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    value: selectedModel || \"\",\n                    label: \"Model\",\n                    onChange: (e)=>handleModelChange(e.target.value),\n                    renderValue: (value)=>{\n                        const model = models.find((m)=>m.name === value);\n                        if (!model) return value;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        flex: 1\n                                    },\n                                    children: model.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    display: \"flex\",\n                                    gap: 0.5,\n                                    children: [\n                                        model.capabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: \"50%\",\n                                                bgcolor: \"white\",\n                                                opacity: 0.7\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        model.capabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: \"50%\",\n                                                bgcolor: \"white\",\n                                                opacity: 0.5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        model.capabilities.supportsCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: \"50%\",\n                                                bgcolor: \"white\",\n                                                opacity: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, void 0);\n                    },\n                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            value: model.name,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\",\n                                width: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"body2\",\n                                                fontWeight: 500,\n                                                children: model.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                children: [\n                                                    formatModelSize(model.size),\n                                                    \" • \",\n                                                    getModelFamily(model)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        display: \"flex\",\n                                        gap: 0.5,\n                                        children: [\n                                            model.capabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                title: \"Tools\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: \"50%\",\n                                                        bgcolor: \"white\",\n                                                        opacity: 0.8\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 23\n                                            }, this),\n                                            model.capabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                title: \"Vision\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: \"50%\",\n                                                        bgcolor: \"white\",\n                                                        opacity: 0.6\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 23\n                                            }, this),\n                                            model.capabilities.supportsCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                title: \"Code\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: \"50%\",\n                                                        bgcolor: \"white\",\n                                                        opacity: 0.4\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 17\n                            }, this)\n                        }, model.name, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ModelSelector.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ServerConfigDialog.tsx":
/*!***********************************************!*\
  !*** ./app/components/ServerConfigDialog.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServerConfigDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,Alert,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,TextField,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Close,Delete,ExpandMore!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ServerConfigDialog({ open, onClose, onConfigUpdate }) {\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    const [configText, setConfigText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (open) {\n            loadConfig();\n        }\n    }, [\n        open\n    ]);\n    const loadConfig = async ()=>{\n        try {\n            const response = await fetch(\"/api/mcp\");\n            if (response.ok) {\n                const data = await response.json();\n                setConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setConfigText(JSON.stringify({\n                    mcpServers: data.mcpServers || {}\n                }, null, 2));\n            }\n        } catch (err) {\n            console.error(\"Error loading config:\", err);\n            setError(\"Failed to load configuration\");\n        }\n    };\n    const handleConfigTextChange = (value)=>{\n        setConfigText(value);\n        setError(null);\n        try {\n            const parsed = JSON.parse(value);\n            if (parsed.mcpServers && typeof parsed.mcpServers === \"object\") {\n                setConfig(parsed);\n            } else {\n                setError('Configuration must have a \"mcpServers\" object');\n            }\n        } catch (err) {\n            setError(\"Invalid JSON syntax\");\n        }\n    };\n    const handleSave = async ()=>{\n        if (error) {\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/mcp\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"update_config\",\n                    config\n                })\n            });\n            if (response.ok) {\n                onConfigUpdate(config);\n                onClose();\n            } else {\n                setError(\"Failed to save configuration\");\n            }\n        } catch (err) {\n            console.error(\"Error saving config:\", err);\n            setError(\"Failed to save configuration\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const addNewServer = ()=>{\n        const newServerName = `server_${Date.now()}`;\n        const newConfig = {\n            ...config,\n            mcpServers: {\n                ...config.mcpServers,\n                [newServerName]: {\n                    command: \"npx\",\n                    args: [\n                        \"@smithery/cli@latest\",\n                        \"run\",\n                        \"example\"\n                    ],\n                    description: \"New MCP server\"\n                }\n            }\n        };\n        setConfig(newConfig);\n        setConfigText(JSON.stringify(newConfig, null, 2));\n    };\n    const removeServer = (serverName)=>{\n        const newConfig = {\n            ...config\n        };\n        delete newConfig.mcpServers[serverName];\n        setConfig(newConfig);\n        setConfigText(JSON.stringify(newConfig, null, 2));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        open: open,\n        onClose: onClose,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"h6\",\n                            children: \"MCP Server Configuration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onClick: onClose,\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        mb: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"body2\",\n                            color: \"text.secondary\",\n                            gutterBottom: true,\n                            children: \"Configure Model Context Protocol (MCP) servers to extend the chat capabilities with external tools.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        severity: \"error\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        mb: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mb: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        children: \"Configured Servers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 26\n                                        }, void 0),\n                                        onClick: addNewServer,\n                                        variant: \"outlined\",\n                                        size: \"small\",\n                                        children: \"Add Server\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            Object.entries(config.mcpServers).map(([name, serverConfig])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 45\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                gap: 2,\n                                                width: \"100%\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"subtitle2\",\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        label: serverConfig.command,\n                                                        size: \"small\",\n                                                        variant: \"outlined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    serverConfig.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"caption\",\n                                                        color: \"text.secondary\",\n                                                        children: serverConfig.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        flexGrow: 1\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: \"small\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            removeServer(name);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Close_Delete_ExpandMore_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            fontSize: \"small\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        gutterBottom: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Command:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            serverConfig.command\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        gutterBottom: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Arguments:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            serverConfig.args.join(\" \")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    serverConfig.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        gutterBottom: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Description:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            serverConfig.description\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                variant: \"h6\",\n                                gutterBottom: true,\n                                children: \"Raw Configuration (JSON)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                multiline: true,\n                                rows: 12,\n                                fullWidth: true,\n                                value: configText,\n                                onChange: (e)=>handleConfigTextChange(e.target.value),\n                                variant: \"outlined\",\n                                placeholder: \"Enter MCP configuration JSON...\",\n                                sx: {\n                                    \"& .MuiInputBase-input\": {\n                                        fontFamily: \"monospace\",\n                                        fontSize: \"0.875rem\"\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        onClick: onClose,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_Alert_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        onClick: handleSave,\n                        variant: \"contained\",\n                        disabled: !!error || loading,\n                        children: loading ? \"Saving...\" : \"Save Configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ServerConfigDialog.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9TZXJ2ZXJDb25maWdEaWFsb2cudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQWdCNUI7QUFNTTtBQVNkLFNBQVN5QixtQkFBbUIsRUFDekNDLElBQUksRUFDSkMsT0FBTyxFQUNQQyxjQUFjLEVBQ1U7SUFDeEIsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUc3QiwrQ0FBUUEsQ0FBWTtRQUFFOEIsWUFBWSxDQUFDO0lBQUU7SUFDakUsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNpQyxPQUFPQyxTQUFTLEdBQUdsQywrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDbUMsU0FBU0MsV0FBVyxHQUFHcEMsK0NBQVFBLENBQUM7SUFFdkNDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXdCLE1BQU07WUFDUlk7UUFDRjtJQUNGLEdBQUc7UUFBQ1o7S0FBSztJQUVULE1BQU1ZLGFBQWE7UUFDakIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixJQUFJRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQ2IsVUFBVTtvQkFBRUMsWUFBWVcsS0FBS1gsVUFBVSxJQUFJLENBQUM7Z0JBQUU7Z0JBQzlDRSxjQUFjVyxLQUFLQyxTQUFTLENBQUM7b0JBQUVkLFlBQVlXLEtBQUtYLFVBQVUsSUFBSSxDQUFDO2dCQUFFLEdBQUcsTUFBTTtZQUM1RTtRQUNGLEVBQUUsT0FBT2UsS0FBSztZQUNaQyxRQUFRYixLQUFLLENBQUMseUJBQXlCWTtZQUN2Q1gsU0FBUztRQUNYO0lBQ0Y7SUFFQSxNQUFNYSx5QkFBeUIsQ0FBQ0M7UUFDOUJoQixjQUFjZ0I7UUFDZGQsU0FBUztRQUVULElBQUk7WUFDRixNQUFNZSxTQUFTTixLQUFLTyxLQUFLLENBQUNGO1lBQzFCLElBQUlDLE9BQU9uQixVQUFVLElBQUksT0FBT21CLE9BQU9uQixVQUFVLEtBQUssVUFBVTtnQkFDOURELFVBQVVvQjtZQUNaLE9BQU87Z0JBQ0xmLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBT1csS0FBSztZQUNaWCxTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU1pQixhQUFhO1FBQ2pCLElBQUlsQixPQUFPO1lBQ1Q7UUFDRjtRQUVBLElBQUk7WUFDRkcsV0FBVztZQUNYLE1BQU1FLFdBQVcsTUFBTUMsTUFBTSxZQUFZO2dCQUN2Q2EsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNWCxLQUFLQyxTQUFTLENBQUM7b0JBQ25CVyxRQUFRO29CQUNSM0I7Z0JBQ0Y7WUFDRjtZQUVBLElBQUlVLFNBQVNFLEVBQUUsRUFBRTtnQkFDZmIsZUFBZUM7Z0JBQ2ZGO1lBQ0YsT0FBTztnQkFDTFEsU0FBUztZQUNYO1FBQ0YsRUFBRSxPQUFPVyxLQUFLO1lBQ1pDLFFBQVFiLEtBQUssQ0FBQyx3QkFBd0JZO1lBQ3RDWCxTQUFTO1FBQ1gsU0FBVTtZQUNSRSxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1vQixlQUFlO1FBQ25CLE1BQU1DLGdCQUFnQixDQUFDLE9BQU8sRUFBRUMsS0FBS0MsR0FBRyxHQUFHLENBQUM7UUFDNUMsTUFBTUMsWUFBWTtZQUNoQixHQUFHaEMsTUFBTTtZQUNURSxZQUFZO2dCQUNWLEdBQUdGLE9BQU9FLFVBQVU7Z0JBQ3BCLENBQUMyQixjQUFjLEVBQUU7b0JBQ2ZJLFNBQVM7b0JBQ1RDLE1BQU07d0JBQUM7d0JBQXdCO3dCQUFPO3FCQUFVO29CQUNoREMsYUFBYTtnQkFDZjtZQUNGO1FBQ0Y7UUFDQWxDLFVBQVUrQjtRQUNWNUIsY0FBY1csS0FBS0MsU0FBUyxDQUFDZ0IsV0FBVyxNQUFNO0lBQ2hEO0lBRUEsTUFBTUksZUFBZSxDQUFDQztRQUNwQixNQUFNTCxZQUFZO1lBQUUsR0FBR2hDLE1BQU07UUFBQztRQUM5QixPQUFPZ0MsVUFBVTlCLFVBQVUsQ0FBQ21DLFdBQVc7UUFDdkNwQyxVQUFVK0I7UUFDVjVCLGNBQWNXLEtBQUtDLFNBQVMsQ0FBQ2dCLFdBQVcsTUFBTTtJQUNoRDtJQUVBLHFCQUNFLDhEQUFDMUQsNE5BQU1BO1FBQUN1QixNQUFNQTtRQUFNQyxTQUFTQTtRQUFTd0MsVUFBUztRQUFLQyxTQUFTOzswQkFDM0QsOERBQUNoRSw0TkFBV0E7MEJBQ1YsNEVBQUNLLDROQUFHQTtvQkFBQzRELFNBQVE7b0JBQU9DLGdCQUFlO29CQUFnQkMsWUFBVzs7c0NBQzVELDhEQUFDN0QsNE5BQVVBOzRCQUFDOEQsU0FBUTtzQ0FBSzs7Ozs7O3NDQUN6Qiw4REFBQzdELDROQUFVQTs0QkFBQzhELFNBQVM5Qzs0QkFBUytDLE1BQUs7c0NBQ2pDLDRFQUFDeEQsNkdBQVNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS2hCLDhEQUFDYiw0TkFBYUE7O2tDQUNaLDhEQUFDSSw0TkFBR0E7d0JBQUNrRSxJQUFJO2tDQUNQLDRFQUFDakUsNE5BQVVBOzRCQUFDOEQsU0FBUTs0QkFBUUksT0FBTTs0QkFBaUJDLFlBQVk7c0NBQUM7Ozs7Ozs7Ozs7O29CQUtqRTNDLHVCQUNDLDhEQUFDdEIsNE5BQUtBO3dCQUFDa0UsVUFBUzt3QkFBUUMsSUFBSTs0QkFBRUosSUFBSTt3QkFBRTtrQ0FDakN6Qzs7Ozs7O2tDQUtMLDhEQUFDekIsNE5BQUdBO3dCQUFDa0UsSUFBSTs7MENBQ1AsOERBQUNsRSw0TkFBR0E7Z0NBQUM0RCxTQUFRO2dDQUFPQyxnQkFBZTtnQ0FBZ0JDLFlBQVc7Z0NBQVNJLElBQUk7O2tEQUN6RSw4REFBQ2pFLDROQUFVQTt3Q0FBQzhELFNBQVE7a0RBQUs7Ozs7OztrREFDekIsOERBQUNqRSw2TkFBTUE7d0NBQ0x5RSx5QkFBVyw4REFBQzFELDhHQUFPQTs7Ozs7d0NBQ25CbUQsU0FBU2hCO3dDQUNUZSxTQUFRO3dDQUNSRSxNQUFLO2tEQUNOOzs7Ozs7Ozs7Ozs7NEJBS0ZPLE9BQU9DLE9BQU8sQ0FBQ3JELE9BQU9FLFVBQVUsRUFBRW9ELEdBQUcsQ0FBQyxDQUFDLENBQUNDLE1BQU1DLGFBQWEsaUJBQzFELDhEQUFDeEUsNk5BQVNBO29DQUFZa0UsSUFBSTt3Q0FBRUosSUFBSTtvQ0FBRTs7c0RBQ2hDLDhEQUFDN0QsNk5BQWdCQTs0Q0FBQ3dFLDBCQUFZLDhEQUFDbEUsOEdBQWNBOzs7OztzREFDM0MsNEVBQUNYLDROQUFHQTtnREFBQzRELFNBQVE7Z0RBQU9FLFlBQVc7Z0RBQVNnQixLQUFLO2dEQUFHQyxPQUFNOztrRUFDcEQsOERBQUM5RSw0TkFBVUE7d0RBQUM4RCxTQUFRO2tFQUFhWTs7Ozs7O2tFQUNqQyw4REFBQ3BFLDZOQUFJQTt3REFDSHlFLE9BQU9KLGFBQWF2QixPQUFPO3dEQUMzQlksTUFBSzt3REFDTEYsU0FBUTs7Ozs7O29EQUVUYSxhQUFhckIsV0FBVyxrQkFDdkIsOERBQUN0RCw0TkFBVUE7d0RBQUM4RCxTQUFRO3dEQUFVSSxPQUFNO2tFQUNqQ1MsYUFBYXJCLFdBQVc7Ozs7OztrRUFHN0IsOERBQUN2RCw0TkFBR0E7d0RBQUNpRixVQUFVOzs7Ozs7a0VBQ2YsOERBQUMvRSw0TkFBVUE7d0RBQ1QrRCxNQUFLO3dEQUNMRCxTQUFTLENBQUNrQjs0REFDUkEsRUFBRUMsZUFBZTs0REFDakIzQixhQUFhbUI7d0RBQ2Y7a0VBRUEsNEVBQUM1RCw4R0FBVUE7NERBQUNxRSxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUkzQiw4REFBQzlFLDZOQUFnQkE7c0RBQ2YsNEVBQUNOLDROQUFHQTs7a0VBQ0YsOERBQUNDLDROQUFVQTt3REFBQzhELFNBQVE7d0RBQVFLLFlBQVk7OzBFQUN0Qyw4REFBQ2lCOzBFQUFPOzs7Ozs7NERBQWlCOzREQUFFVCxhQUFhdkIsT0FBTzs7Ozs7OztrRUFFakQsOERBQUNwRCw0TkFBVUE7d0RBQUM4RCxTQUFRO3dEQUFRSyxZQUFZOzswRUFDdEMsOERBQUNpQjswRUFBTzs7Ozs7OzREQUFtQjs0REFBRVQsYUFBYXRCLElBQUksQ0FBQ2dDLElBQUksQ0FBQzs7Ozs7OztvREFFckRWLGFBQWFyQixXQUFXLGtCQUN2Qiw4REFBQ3RELDROQUFVQTt3REFBQzhELFNBQVE7d0RBQVFLLFlBQVk7OzBFQUN0Qyw4REFBQ2lCOzBFQUFPOzs7Ozs7NERBQXFCOzREQUFFVCxhQUFhckIsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQ0FwQ2pEb0I7Ozs7Ozs7Ozs7O2tDQThDcEIsOERBQUMzRSw0TkFBR0E7OzBDQUNGLDhEQUFDQyw0TkFBVUE7Z0NBQUM4RCxTQUFRO2dDQUFLSyxZQUFZOzBDQUFDOzs7Ozs7MENBR3RDLDhEQUFDckUsNk5BQVNBO2dDQUNSd0YsU0FBUztnQ0FDVEMsTUFBTTtnQ0FDTjdCLFNBQVM7Z0NBQ1RuQixPQUFPakI7Z0NBQ1BrRSxVQUFVLENBQUNQLElBQU0zQyx1QkFBdUIyQyxFQUFFUSxNQUFNLENBQUNsRCxLQUFLO2dDQUN0RHVCLFNBQVE7Z0NBQ1I0QixhQUFZO2dDQUNackIsSUFBSTtvQ0FDRix5QkFBeUI7d0NBQ3ZCc0IsWUFBWTt3Q0FDWlIsVUFBVTtvQ0FDWjtnQ0FDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtOLDhEQUFDdkYsNk5BQWFBOztrQ0FDWiw4REFBQ0MsNk5BQU1BO3dCQUFDa0UsU0FBUzlDO2tDQUFTOzs7Ozs7a0NBQzFCLDhEQUFDcEIsNk5BQU1BO3dCQUNMa0UsU0FBU3JCO3dCQUNUb0IsU0FBUTt3QkFDUjhCLFVBQVUsQ0FBQyxDQUFDcEUsU0FBU0U7a0NBRXBCQSxVQUFVLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtuQyIsInNvdXJjZXMiOlsid2VicGFjazovL29sbGFtYS1tY3AtY2hhdC8uL2FwcC9jb21wb25lbnRzL1NlcnZlckNvbmZpZ0RpYWxvZy50c3g/MDI3NCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHtcbiAgRGlhbG9nLFxuICBEaWFsb2dUaXRsZSxcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nQWN0aW9ucyxcbiAgQnV0dG9uLFxuICBUZXh0RmllbGQsXG4gIEJveCxcbiAgVHlwb2dyYXBoeSxcbiAgSWNvbkJ1dHRvbixcbiAgQWxlcnQsXG4gIEFjY29yZGlvbixcbiAgQWNjb3JkaW9uU3VtbWFyeSxcbiAgQWNjb3JkaW9uRGV0YWlscyxcbiAgQ2hpcCxcbn0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XG5pbXBvcnQge1xuICBDbG9zZSBhcyBDbG9zZUljb24sXG4gIEV4cGFuZE1vcmUgYXMgRXhwYW5kTW9yZUljb24sXG4gIEFkZCBhcyBBZGRJY29uLFxuICBEZWxldGUgYXMgRGVsZXRlSWNvbixcbn0gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbCc7XG5pbXBvcnQgeyBNQ1BDb25maWcsIE1DUFNlcnZlckNvbmZpZyB9IGZyb20gJ0AvdHlwZXMnO1xuXG5pbnRlcmZhY2UgU2VydmVyQ29uZmlnRGlhbG9nUHJvcHMge1xuICBvcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBvbkNvbmZpZ1VwZGF0ZTogKGNvbmZpZzogTUNQQ29uZmlnKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXJ2ZXJDb25maWdEaWFsb2coe1xuICBvcGVuLFxuICBvbkNsb3NlLFxuICBvbkNvbmZpZ1VwZGF0ZSxcbn06IFNlcnZlckNvbmZpZ0RpYWxvZ1Byb3BzKSB7XG4gIGNvbnN0IFtjb25maWcsIHNldENvbmZpZ10gPSB1c2VTdGF0ZTxNQ1BDb25maWc+KHsgbWNwU2VydmVyczoge30gfSk7XG4gIGNvbnN0IFtjb25maWdUZXh0LCBzZXRDb25maWdUZXh0XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG9wZW4pIHtcbiAgICAgIGxvYWRDb25maWcoKTtcbiAgICB9XG4gIH0sIFtvcGVuXSk7XG5cbiAgY29uc3QgbG9hZENvbmZpZyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9tY3AnKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBzZXRDb25maWcoeyBtY3BTZXJ2ZXJzOiBkYXRhLm1jcFNlcnZlcnMgfHwge30gfSk7XG4gICAgICAgIHNldENvbmZpZ1RleHQoSlNPTi5zdHJpbmdpZnkoeyBtY3BTZXJ2ZXJzOiBkYXRhLm1jcFNlcnZlcnMgfHwge30gfSwgbnVsbCwgMikpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBjb25maWc6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBjb25maWd1cmF0aW9uJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNvbmZpZ1RleHRDaGFuZ2UgPSAodmFsdWU6IHN0cmluZykgPT4ge1xuICAgIHNldENvbmZpZ1RleHQodmFsdWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UodmFsdWUpO1xuICAgICAgaWYgKHBhcnNlZC5tY3BTZXJ2ZXJzICYmIHR5cGVvZiBwYXJzZWQubWNwU2VydmVycyA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgc2V0Q29uZmlnKHBhcnNlZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRFcnJvcignQ29uZmlndXJhdGlvbiBtdXN0IGhhdmUgYSBcIm1jcFNlcnZlcnNcIiBvYmplY3QnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldEVycm9yKCdJbnZhbGlkIEpTT04gc3ludGF4Jyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGVycm9yKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL21jcCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgYWN0aW9uOiAndXBkYXRlX2NvbmZpZycsXG4gICAgICAgICAgY29uZmlnLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgb25Db25maWdVcGRhdGUoY29uZmlnKTtcbiAgICAgICAgb25DbG9zZSgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGNvbmZpZ3VyYXRpb24nKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBjb25maWc6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gc2F2ZSBjb25maWd1cmF0aW9uJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBhZGROZXdTZXJ2ZXIgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3U2VydmVyTmFtZSA9IGBzZXJ2ZXJfJHtEYXRlLm5vdygpfWA7XG4gICAgY29uc3QgbmV3Q29uZmlnID0ge1xuICAgICAgLi4uY29uZmlnLFxuICAgICAgbWNwU2VydmVyczoge1xuICAgICAgICAuLi5jb25maWcubWNwU2VydmVycyxcbiAgICAgICAgW25ld1NlcnZlck5hbWVdOiB7XG4gICAgICAgICAgY29tbWFuZDogJ25weCcsXG4gICAgICAgICAgYXJnczogWydAc21pdGhlcnkvY2xpQGxhdGVzdCcsICdydW4nLCAnZXhhbXBsZSddLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnTmV3IE1DUCBzZXJ2ZXInLFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9O1xuICAgIHNldENvbmZpZyhuZXdDb25maWcpO1xuICAgIHNldENvbmZpZ1RleHQoSlNPTi5zdHJpbmdpZnkobmV3Q29uZmlnLCBudWxsLCAyKSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlU2VydmVyID0gKHNlcnZlck5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5ld0NvbmZpZyA9IHsgLi4uY29uZmlnIH07XG4gICAgZGVsZXRlIG5ld0NvbmZpZy5tY3BTZXJ2ZXJzW3NlcnZlck5hbWVdO1xuICAgIHNldENvbmZpZyhuZXdDb25maWcpO1xuICAgIHNldENvbmZpZ1RleHQoSlNPTi5zdHJpbmdpZnkobmV3Q29uZmlnLCBudWxsLCAyKSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8RGlhbG9nIG9wZW49e29wZW59IG9uQ2xvc2U9e29uQ2xvc2V9IG1heFdpZHRoPVwibWRcIiBmdWxsV2lkdGg+XG4gICAgICA8RGlhbG9nVGl0bGU+XG4gICAgICAgIDxCb3ggZGlzcGxheT1cImZsZXhcIiBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBhbGlnbkl0ZW1zPVwiY2VudGVyXCI+XG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCI+TUNQIFNlcnZlciBDb25maWd1cmF0aW9uPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgIDxJY29uQnV0dG9uIG9uQ2xpY2s9e29uQ2xvc2V9IHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgICAgPENsb3NlSWNvbiAvPlxuICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cbiAgICAgICAgPC9Cb3g+XG4gICAgICA8L0RpYWxvZ1RpdGxlPlxuXG4gICAgICA8RGlhbG9nQ29udGVudD5cbiAgICAgICAgPEJveCBtYj17Mn0+XG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0LnNlY29uZGFyeVwiIGd1dHRlckJvdHRvbT5cbiAgICAgICAgICAgIENvbmZpZ3VyZSBNb2RlbCBDb250ZXh0IFByb3RvY29sIChNQ1ApIHNlcnZlcnMgdG8gZXh0ZW5kIHRoZSBjaGF0IGNhcGFiaWxpdGllcyB3aXRoIGV4dGVybmFsIHRvb2xzLlxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgPC9Cb3g+XG5cbiAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICA8QWxlcnQgc2V2ZXJpdHk9XCJlcnJvclwiIHN4PXt7IG1iOiAyIH19PlxuICAgICAgICAgICAge2Vycm9yfVxuICAgICAgICAgIDwvQWxlcnQ+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIFNlcnZlciBPdmVydmlldyAqL31cbiAgICAgICAgPEJveCBtYj17M30+XG4gICAgICAgICAgPEJveCBkaXNwbGF5PVwiZmxleFwiIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIGFsaWduSXRlbXM9XCJjZW50ZXJcIiBtYj17Mn0+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIj5Db25maWd1cmVkIFNlcnZlcnM8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHN0YXJ0SWNvbj17PEFkZEljb24gLz59XG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2FkZE5ld1NlcnZlcn1cbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgQWRkIFNlcnZlclxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9Cb3g+XG5cbiAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoY29uZmlnLm1jcFNlcnZlcnMpLm1hcCgoW25hbWUsIHNlcnZlckNvbmZpZ10pID0+IChcbiAgICAgICAgICAgIDxBY2NvcmRpb24ga2V5PXtuYW1lfSBzeD17eyBtYjogMSB9fT5cbiAgICAgICAgICAgICAgPEFjY29yZGlvblN1bW1hcnkgZXhwYW5kSWNvbj17PEV4cGFuZE1vcmVJY29uIC8+fT5cbiAgICAgICAgICAgICAgICA8Qm94IGRpc3BsYXk9XCJmbGV4XCIgYWxpZ25JdGVtcz1cImNlbnRlclwiIGdhcD17Mn0gd2lkdGg9XCIxMDAlXCI+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwic3VidGl0bGUyXCI+e25hbWV9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgPENoaXBcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9e3NlcnZlckNvbmZpZy5jb21tYW5kfVxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIHtzZXJ2ZXJDb25maWcuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiY2FwdGlvblwiIGNvbG9yPVwidGV4dC5zZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7c2VydmVyQ29uZmlnLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPEJveCBmbGV4R3Jvdz17MX0gLz5cbiAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICByZW1vdmVTZXJ2ZXIobmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxEZWxldGVJY29uIGZvbnRTaXplPVwic21hbGxcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9JY29uQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICA8L0FjY29yZGlvblN1bW1hcnk+XG4gICAgICAgICAgICAgIDxBY2NvcmRpb25EZXRhaWxzPlxuICAgICAgICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBndXR0ZXJCb3R0b20+XG4gICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+Q29tbWFuZDo8L3N0cm9uZz4ge3NlcnZlckNvbmZpZy5jb21tYW5kfVxuICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgZ3V0dGVyQm90dG9tPlxuICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPkFyZ3VtZW50czo8L3N0cm9uZz4ge3NlcnZlckNvbmZpZy5hcmdzLmpvaW4oJyAnKX1cbiAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgIHtzZXJ2ZXJDb25maWcuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBndXR0ZXJCb3R0b20+XG4gICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz5EZXNjcmlwdGlvbjo8L3N0cm9uZz4ge3NlcnZlckNvbmZpZy5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgPC9BY2NvcmRpb25EZXRhaWxzPlxuICAgICAgICAgICAgPC9BY2NvcmRpb24+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvQm94PlxuXG4gICAgICAgIHsvKiBSYXcgSlNPTiBFZGl0b3IgKi99XG4gICAgICAgIDxCb3g+XG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCIgZ3V0dGVyQm90dG9tPlxuICAgICAgICAgICAgUmF3IENvbmZpZ3VyYXRpb24gKEpTT04pXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgIDxUZXh0RmllbGRcbiAgICAgICAgICAgIG11bHRpbGluZVxuICAgICAgICAgICAgcm93cz17MTJ9XG4gICAgICAgICAgICBmdWxsV2lkdGhcbiAgICAgICAgICAgIHZhbHVlPXtjb25maWdUZXh0fVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDb25maWdUZXh0Q2hhbmdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIE1DUCBjb25maWd1cmF0aW9uIEpTT04uLi5cIlxuICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgJyYgLk11aUlucHV0QmFzZS1pbnB1dCc6IHtcbiAgICAgICAgICAgICAgICBmb250RmFtaWx5OiAnbW9ub3NwYWNlJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuODc1cmVtJyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9Cb3g+XG4gICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG5cbiAgICAgIDxEaWFsb2dBY3Rpb25zPlxuICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e29uQ2xvc2V9PkNhbmNlbDwvQnV0dG9uPlxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZX1cbiAgICAgICAgICB2YXJpYW50PVwiY29udGFpbmVkXCJcbiAgICAgICAgICBkaXNhYmxlZD17ISFlcnJvciB8fCBsb2FkaW5nfVxuICAgICAgICA+XG4gICAgICAgICAge2xvYWRpbmcgPyAnU2F2aW5nLi4uJyA6ICdTYXZlIENvbmZpZ3VyYXRpb24nfVxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgIDwvRGlhbG9nQWN0aW9ucz5cbiAgICA8L0RpYWxvZz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiRGlhbG9nIiwiRGlhbG9nVGl0bGUiLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nQWN0aW9ucyIsIkJ1dHRvbiIsIlRleHRGaWVsZCIsIkJveCIsIlR5cG9ncmFwaHkiLCJJY29uQnV0dG9uIiwiQWxlcnQiLCJBY2NvcmRpb24iLCJBY2NvcmRpb25TdW1tYXJ5IiwiQWNjb3JkaW9uRGV0YWlscyIsIkNoaXAiLCJDbG9zZSIsIkNsb3NlSWNvbiIsIkV4cGFuZE1vcmUiLCJFeHBhbmRNb3JlSWNvbiIsIkFkZCIsIkFkZEljb24iLCJEZWxldGUiLCJEZWxldGVJY29uIiwiU2VydmVyQ29uZmlnRGlhbG9nIiwib3BlbiIsIm9uQ2xvc2UiLCJvbkNvbmZpZ1VwZGF0ZSIsImNvbmZpZyIsInNldENvbmZpZyIsIm1jcFNlcnZlcnMiLCJjb25maWdUZXh0Iiwic2V0Q29uZmlnVGV4dCIsImVycm9yIiwic2V0RXJyb3IiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImxvYWRDb25maWciLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJkYXRhIiwianNvbiIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnIiLCJjb25zb2xlIiwiaGFuZGxlQ29uZmlnVGV4dENoYW5nZSIsInZhbHVlIiwicGFyc2VkIiwicGFyc2UiLCJoYW5kbGVTYXZlIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJhY3Rpb24iLCJhZGROZXdTZXJ2ZXIiLCJuZXdTZXJ2ZXJOYW1lIiwiRGF0ZSIsIm5vdyIsIm5ld0NvbmZpZyIsImNvbW1hbmQiLCJhcmdzIiwiZGVzY3JpcHRpb24iLCJyZW1vdmVTZXJ2ZXIiLCJzZXJ2ZXJOYW1lIiwibWF4V2lkdGgiLCJmdWxsV2lkdGgiLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwidmFyaWFudCIsIm9uQ2xpY2siLCJzaXplIiwibWIiLCJjb2xvciIsImd1dHRlckJvdHRvbSIsInNldmVyaXR5Iiwic3giLCJzdGFydEljb24iLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwibmFtZSIsInNlcnZlckNvbmZpZyIsImV4cGFuZEljb24iLCJnYXAiLCJ3aWR0aCIsImxhYmVsIiwiZmxleEdyb3ciLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiZm9udFNpemUiLCJzdHJvbmciLCJqb2luIiwibXVsdGlsaW5lIiwicm93cyIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJmb250RmFtaWx5IiwiZGlzYWJsZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ServerConfigDialog.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./app/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* harmony import */ var _theme_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../theme/theme */ \"(ssr)/./app/theme/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ThemeProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        theme: _theme_theme__WEBPACK_IMPORTED_MODULE_2__.theme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ThemeProvider.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMEI7QUFDK0M7QUFDckI7QUFDYjtBQU14QixTQUFTQyxjQUFjLEVBQUVJLFFBQVEsRUFBc0I7SUFDcEUscUJBQ0UsOERBQUNILDREQUFnQkE7UUFBQ0UsT0FBT0EsK0NBQUtBOzswQkFDNUIsOERBQUNELGlFQUFXQTs7Ozs7WUFDWEU7Ozs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL29sbGFtYS1tY3AtY2hhdC8uL2FwcC9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4P2MyY2MiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBNdWlUaGVtZVByb3ZpZGVyIH0gZnJvbSAnQG11aS9tYXRlcmlhbC9zdHlsZXMnO1xuaW1wb3J0IENzc0Jhc2VsaW5lIGZyb20gJ0BtdWkvbWF0ZXJpYWwvQ3NzQmFzZWxpbmUnO1xuaW1wb3J0IHsgdGhlbWUgfSBmcm9tICcuLi90aGVtZS90aGVtZSc7XG5cbmludGVyZmFjZSBUaGVtZVByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE11aVRoZW1lUHJvdmlkZXIgdGhlbWU9e3RoZW1lfT5cbiAgICAgIDxDc3NCYXNlbGluZSAvPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTXVpVGhlbWVQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJNdWlUaGVtZVByb3ZpZGVyIiwiQ3NzQmFzZWxpbmUiLCJ0aGVtZSIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ModelSelector */ \"(ssr)/./app/components/ModelSelector.tsx\");\n/* harmony import */ var _components_ServerConfigDialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ServerConfigDialog */ \"(ssr)/./app/components/ServerConfigDialog.tsx\");\n/* harmony import */ var _components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ChatWindow/ChatWindow */ \"(ssr)/./app/components/ChatWindow/ChatWindow.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction HomePage() {\n    const theme = (0,_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configDialogOpen, setConfigDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [mcpConfig, setMcpConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    const [availableTools, setAvailableTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversationId, setActiveConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelCapabilities, setModelCapabilities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMCPConfig();\n        loadConversations();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSidebarOpen(!isMobile);\n    }, [\n        isMobile\n    ]);\n    const loadMCPConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/mcp\");\n            if (response.ok) {\n                const data = await response.json();\n                setMcpConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setAvailableTools(data.tools || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading MCP config:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadConversations = ()=>{\n        // Load conversations from localStorage\n        const saved = localStorage.getItem(\"ollama-chat-conversations\");\n        if (saved) {\n            try {\n                const parsed = JSON.parse(saved);\n                setConversations(parsed.map((conv)=>({\n                        ...conv,\n                        timestamp: new Date(conv.timestamp),\n                        messages: conv.messages.map((msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            }))\n                    })));\n            } catch (error) {\n                console.error(\"Error loading conversations:\", error);\n            }\n        }\n    };\n    const saveConversations = (convs)=>{\n        localStorage.setItem(\"ollama-chat-conversations\", JSON.stringify(convs));\n    };\n    const handleConfigUpdate = (newConfig)=>{\n        setMcpConfig(newConfig);\n        loadMCPConfig(); // Reload to get updated tools\n    };\n    const handleRefresh = ()=>{\n        loadMCPConfig();\n    };\n    const createNewConversation = ()=>{\n        const newConversation = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n            title: \"New Conversation\",\n            lastMessage: \"\",\n            timestamp: new Date(),\n            messageCount: 0,\n            messages: []\n        };\n        const updatedConversations = [\n            newConversation,\n            ...conversations\n        ];\n        setConversations(updatedConversations);\n        setActiveConversationId(newConversation.id);\n        saveConversations(updatedConversations);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const selectConversation = (id)=>{\n        setActiveConversationId(id);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteConversation = (id)=>{\n        const updatedConversations = conversations.filter((conv)=>conv.id !== id);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n        if (activeConversationId === id) {\n            setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);\n        }\n    };\n    const renameConversation = (id, newTitle)=>{\n        const updatedConversations = conversations.map((conv)=>conv.id === id ? {\n                ...conv,\n                title: newTitle\n            } : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const updateConversationWithMessage = (message)=>{\n        const activeConv = conversations.find((conv)=>conv.id === activeConversationId);\n        if (!activeConv) return;\n        const updatedConversation = {\n            ...activeConv,\n            messages: [\n                ...activeConv.messages,\n                message\n            ],\n            lastMessage: message.content,\n            timestamp: new Date(),\n            messageCount: activeConv.messageCount + 1,\n            title: activeConv.title === \"New Conversation\" && message.role === \"user\" ? message.content.substring(0, 50) + (message.content.length > 50 ? \"...\" : \"\") : activeConv.title\n        };\n        const updatedConversations = conversations.map((conv)=>conv.id === activeConversationId ? updatedConversation : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const getActiveConversation = ()=>{\n        return conversations.find((conv)=>conv.id === activeConversationId) || null;\n    };\n    const serverCount = Object.keys(mcpConfig.mcpServers).length;\n    const isConnected = selectedModel !== null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            height: \"100vh\",\n            display: \"flex\",\n            overflow: \"hidden\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    width: sidebarOpen ? 320 : 0,\n                    transition: \"width 0.3s ease\",\n                    overflow: \"hidden\",\n                    borderRight: sidebarOpen ? \"1px solid rgba(255, 255, 255, 0.1)\" : \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        width: 320,\n                        height: \"100%\",\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        backdropFilter: \"blur(20px)\",\n                        background: \"rgba(255, 255, 255, 0.03)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sx: {\n                                p: 3,\n                                borderBottom: \"1px solid rgba(255, 255, 255, 0.1)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        mb: 2,\n                                        fontWeight: 600\n                                    },\n                                    children: \"Ollama Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    selectedModel: selectedModel,\n                                    onModelSelect: setSelectedModel,\n                                    onModelCapabilitiesChange: setModelCapabilities\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sx: {\n                                flex: 1,\n                                overflow: \"auto\",\n                                p: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    sx: {\n                                        mb: 2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        fullWidth: true,\n                                        variant: \"outlined\",\n                                        onClick: createNewConversation,\n                                        sx: {\n                                            mb: 2\n                                        },\n                                        children: \"New Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"text.secondary\",\n                                    sx: {\n                                        textAlign: \"center\",\n                                        py: 4\n                                    },\n                                    children: \"No conversations yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this) : conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        onClick: ()=>selectConversation(conversation.id),\n                                        sx: {\n                                            p: 2,\n                                            mb: 1,\n                                            borderRadius: 2,\n                                            cursor: \"pointer\",\n                                            background: conversation.id === activeConversationId ? \"rgba(255, 255, 255, 0.1)\" : \"transparent\",\n                                            border: conversation.id === activeConversationId ? \"1px solid rgba(255, 255, 255, 0.2)\" : \"1px solid transparent\",\n                                            transition: \"all 0.2s ease\",\n                                            \"&:hover\": {\n                                                background: \"rgba(255, 255, 255, 0.05)\"\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                variant: \"body2\",\n                                                fontWeight: 500,\n                                                noWrap: true,\n                                                children: conversation.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                noWrap: true,\n                                                children: [\n                                                    conversation.messageCount,\n                                                    \" messages\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, conversation.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            sx: {\n                                p: 2,\n                                borderTop: \"1px solid rgba(255, 255, 255, 0.1)\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                fullWidth: true,\n                                variant: \"outlined\",\n                                onClick: ()=>setConfigDialogOpen(true),\n                                size: \"small\",\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    display: \"flex\",\n                    flexDirection: \"column\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            p: 2,\n                            borderBottom: \"1px solid rgba(255, 255, 255, 0.1)\",\n                            backdropFilter: \"blur(20px)\",\n                            background: \"rgba(255, 255, 255, 0.03)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                sx: {\n                                    mr: 2,\n                                    minWidth: \"auto\",\n                                    p: 1\n                                },\n                                children: \"☰\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                variant: \"h6\",\n                                sx: {\n                                    flex: 1\n                                },\n                                children: getActiveConversation()?.title || \"Select a conversation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            selectedModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    gap: 1\n                                },\n                                children: [\n                                    modelCapabilities?.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        sx: {\n                                            px: 1,\n                                            py: 0.5,\n                                            borderRadius: 1,\n                                            background: \"rgba(255, 255, 255, 0.1)\",\n                                            fontSize: \"0.75rem\"\n                                        },\n                                        children: \"Tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this),\n                                    modelCapabilities?.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        sx: {\n                                            px: 1,\n                                            py: 0.5,\n                                            borderRadius: 1,\n                                            background: \"rgba(255, 255, 255, 0.1)\",\n                                            fontSize: \"0.75rem\"\n                                        },\n                                        children: \"Vision\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            flex: 1,\n                            overflow: \"hidden\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            selectedModel: selectedModel,\n                            availableTools: availableTools,\n                            conversation: getActiveConversation(),\n                            onMessageSent: updateConversationWithMessage,\n                            modelCapabilities: modelCapabilities\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ServerConfigDialog__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                open: configDialogOpen,\n                onClose: ()=>setConfigDialogOpen(false),\n                onConfigUpdate: handleConfigUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/theme/theme.ts":
/*!****************************!*\
  !*** ./app/theme/theme.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   theme: () => (/* binding */ theme)\n/* harmony export */ });\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/createTheme.js\");\n/* __next_internal_client_entry_do_not_use__ theme auto */ \n// Minimalist black and white glassmorphism palette\nconst palette = {\n    primary: {\n        main: \"#ffffff\",\n        light: \"#ffffff\",\n        dark: \"#f5f5f5\",\n        contrastText: \"#000000\",\n        50: \"#ffffff\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    secondary: {\n        main: \"#000000\",\n        light: \"#424242\",\n        dark: \"#000000\",\n        contrastText: \"#ffffff\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    success: {\n        main: \"#ffffff\",\n        light: \"#ffffff\",\n        dark: \"#f5f5f5\",\n        50: \"#ffffff\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    warning: {\n        main: \"#9e9e9e\",\n        light: \"#bdbdbd\",\n        dark: \"#757575\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    error: {\n        main: \"#757575\",\n        light: \"#9e9e9e\",\n        dark: \"#424242\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    background: {\n        default: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\",\n        paper: \"rgba(255, 255, 255, 0.05)\",\n        glass: \"rgba(255, 255, 255, 0.03)\",\n        glassDark: \"rgba(0, 0, 0, 0.2)\"\n    },\n    text: {\n        primary: \"#ffffff\",\n        secondary: \"rgba(255, 255, 255, 0.7)\",\n        disabled: \"rgba(255, 255, 255, 0.3)\"\n    },\n    divider: \"rgba(255, 255, 255, 0.08)\",\n    action: {\n        hover: \"rgba(255, 255, 255, 0.05)\",\n        selected: \"rgba(255, 255, 255, 0.08)\",\n        disabled: \"rgba(255, 255, 255, 0.2)\",\n        disabledBackground: \"rgba(255, 255, 255, 0.05)\"\n    }\n};\n// Modern typography with better hierarchy\nconst typography = {\n    fontFamily: [\n        '\"Inter\"',\n        \"-apple-system\",\n        \"BlinkMacSystemFont\",\n        '\"Segoe UI\"',\n        \"Roboto\",\n        '\"Helvetica Neue\"',\n        \"Arial\",\n        \"sans-serif\"\n    ].join(\",\"),\n    h1: {\n        fontSize: \"2.5rem\",\n        fontWeight: 800,\n        lineHeight: 1.1,\n        letterSpacing: \"-0.025em\",\n        background: \"linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%)\",\n        WebkitBackgroundClip: \"text\",\n        WebkitTextFillColor: \"transparent\",\n        backgroundClip: \"text\"\n    },\n    h2: {\n        fontSize: \"2rem\",\n        fontWeight: 700,\n        lineHeight: 1.2,\n        letterSpacing: \"-0.025em\"\n    },\n    h3: {\n        fontSize: \"1.75rem\",\n        fontWeight: 600,\n        lineHeight: 1.3,\n        letterSpacing: \"-0.015em\"\n    },\n    h4: {\n        fontSize: \"1.5rem\",\n        fontWeight: 600,\n        lineHeight: 1.3,\n        letterSpacing: \"-0.015em\"\n    },\n    h5: {\n        fontSize: \"1.25rem\",\n        fontWeight: 600,\n        lineHeight: 1.4,\n        letterSpacing: \"-0.01em\"\n    },\n    h6: {\n        fontSize: \"1.125rem\",\n        fontWeight: 600,\n        lineHeight: 1.4,\n        letterSpacing: \"-0.01em\"\n    },\n    body1: {\n        fontSize: \"1rem\",\n        lineHeight: 1.6,\n        fontWeight: 400\n    },\n    body2: {\n        fontSize: \"0.875rem\",\n        lineHeight: 1.5,\n        fontWeight: 400\n    },\n    caption: {\n        fontSize: \"0.75rem\",\n        lineHeight: 1.4,\n        fontWeight: 500,\n        letterSpacing: \"0.025em\",\n        textTransform: \"uppercase\"\n    },\n    button: {\n        fontWeight: 600,\n        letterSpacing: \"0.025em\",\n        textTransform: \"none\"\n    }\n};\n// Custom spacing and breakpoints\nconst spacing = 8;\nconst breakpoints = {\n    values: {\n        xs: 0,\n        sm: 640,\n        md: 768,\n        lg: 1024,\n        xl: 1280\n    }\n};\n// Modern glassmorphism component overrides\nconst components = {\n    MuiCssBaseline: {\n        styleOverrides: {\n            \"*\": {\n                boxSizing: \"border-box\"\n            },\n            html: {\n                height: \"100%\"\n            },\n            body: {\n                height: \"100%\",\n                margin: 0,\n                padding: 0,\n                background: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\",\n                backgroundAttachment: \"fixed\",\n                scrollbarWidth: \"thin\",\n                scrollbarColor: \"rgba(255, 255, 255, 0.2) transparent\",\n                \"&::-webkit-scrollbar\": {\n                    width: \"6px\"\n                },\n                \"&::-webkit-scrollbar-track\": {\n                    background: \"transparent\"\n                },\n                \"&::-webkit-scrollbar-thumb\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    borderRadius: \"10px\",\n                    border: \"none\"\n                },\n                \"&::-webkit-scrollbar-thumb:hover\": {\n                    background: \"rgba(255, 255, 255, 0.3)\"\n                }\n            },\n            \"#__next\": {\n                height: \"100%\"\n            }\n        }\n    },\n    MuiButton: {\n        styleOverrides: {\n            root: {\n                textTransform: \"none\",\n                borderRadius: \"16px\",\n                fontWeight: 600,\n                padding: \"12px 24px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.05)\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                color: \"white\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    transform: \"translateY(-1px)\",\n                    boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.3)\"\n                },\n                \"&:active\": {\n                    transform: \"translateY(0px)\"\n                }\n            },\n            contained: {\n                background: \"linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%)\",\n                border: \"none\",\n                color: \"#000000\",\n                boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.2)\",\n                \"&:hover\": {\n                    background: \"linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)\",\n                    boxShadow: \"0 15px 40px rgba(0, 0, 0, 0.3)\"\n                }\n            },\n            outlined: {\n                borderColor: \"rgba(255, 255, 255, 0.2)\",\n                \"&:hover\": {\n                    borderColor: \"rgba(255, 255, 255, 0.3)\",\n                    background: \"rgba(255, 255, 255, 0.05)\"\n                }\n            }\n        }\n    },\n    MuiPaper: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"24px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.15)\",\n                    transform: \"translateY(-2px)\",\n                    boxShadow: \"0 30px 60px rgba(0, 0, 0, 0.15)\"\n                }\n            }\n        }\n    },\n    MuiTextField: {\n        styleOverrides: {\n            root: {\n                \"& .MuiOutlinedInput-root\": {\n                    borderRadius: \"16px\",\n                    backdropFilter: \"blur(20px)\",\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                    color: \"white\",\n                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                    \"& fieldset\": {\n                        border: \"none\"\n                    },\n                    \"&:hover\": {\n                        background: \"rgba(255, 255, 255, 0.15)\",\n                        transform: \"translateY(-1px)\"\n                    },\n                    \"&.Mui-focused\": {\n                        background: \"rgba(255, 255, 255, 0.2)\",\n                        boxShadow: \"0 0 0 2px rgba(99, 102, 241, 0.5)\"\n                    }\n                },\n                \"& .MuiInputLabel-root\": {\n                    color: \"rgba(255, 255, 255, 0.7)\",\n                    \"&.Mui-focused\": {\n                        color: \"#6366f1\"\n                    }\n                },\n                \"& .MuiOutlinedInput-input\": {\n                    color: \"white\",\n                    \"&::placeholder\": {\n                        color: \"rgba(255, 255, 255, 0.5)\",\n                        opacity: 1\n                    }\n                }\n            }\n        }\n    },\n    MuiChip: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"12px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                color: \"white\",\n                fontWeight: 500,\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    transform: \"translateY(-1px)\"\n                }\n            }\n        }\n    },\n    MuiIconButton: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"12px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                color: \"white\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    transform: \"translateY(-2px)\",\n                    boxShadow: \"0 10px 20px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        }\n    },\n    MuiDrawer: {\n        styleOverrides: {\n            paper: {\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.05)\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                borderLeft: \"none\"\n            }\n        }\n    },\n    MuiAppBar: {\n        styleOverrides: {\n            root: {\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                borderTop: \"none\",\n                borderLeft: \"none\",\n                borderRight: \"none\",\n                boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.1)\"\n            }\n        }\n    }\n};\n// Create the theme\nconst themeOptions = {\n    palette,\n    typography,\n    spacing,\n    breakpoints,\n    components,\n    transitions: {\n        duration: {\n            shortest: 150,\n            shorter: 200,\n            short: 250,\n            standard: 300,\n            complex: 375,\n            enteringScreen: 225,\n            leavingScreen: 195\n        },\n        easing: {\n            easeInOut: \"cubic-bezier(0.4, 0, 0.2, 1)\",\n            easeOut: \"cubic-bezier(0.0, 0, 0.2, 1)\",\n            easeIn: \"cubic-bezier(0.4, 0, 1, 1)\",\n            sharp: \"cubic-bezier(0.4, 0, 0.6, 1)\"\n        }\n    }\n};\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(themeOptions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/theme/theme.ts\n");

/***/ }),

/***/ "(rsc)/./app/components/AnimatedBackground.tsx":
/*!***********************************************!*\
  !*** ./app/components/AnimatedBackground.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\components\AnimatedBackground.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./app/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/ThemeProvider */ \"(rsc)/./app/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_AnimatedBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/AnimatedBackground */ \"(rsc)/./app/components/AnimatedBackground.tsx\");\n\n\n\nconst metadata = {\n    title: \"Ollama MCP Chat\",\n    description: \"A dynamic chat interface for Ollama LLM with integrated Smithery.ai MCP tool servers\",\n    keywords: [\n        \"ollama\",\n        \"mcp\",\n        \"chat\",\n        \"ai\",\n        \"llm\",\n        \"smithery\"\n    ],\n    authors: [\n        {\n            name: \"Ollama MCP Chat\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnimatedBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/framer-motion","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/hoist-non-react-statics","vendor-chunks/object-assign","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/@popperjs","vendor-chunks/react-transition-group","vendor-chunks/uuid","vendor-chunks/react-is"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();