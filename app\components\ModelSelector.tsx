'use client';

import React, { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Avatar,
  Tooltip,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Visibility as VisionIcon,
  Build as ToolsIcon,
  Code as CodeIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { OllamaModel } from '@/types';

interface ModelSelectorProps {
  selectedModel: string | null;
  onModelSelect: (model: string) => void;
  onModelCapabilitiesChange?: (capabilities: any) => void;
}

export default function ModelSelector({
  selectedModel,
  onModelSelect,
  onModelCapabilitiesChange
}: ModelSelectorProps) {
  const [models, setModels] = useState<OllamaModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/ollama');
      
      if (!response.ok) {
        throw new Error('Failed to fetch models');
      }

      const data = await response.json();
      setModels(data.models || []);

      // Auto-select first model if none selected
      if (!selectedModel && data.models && data.models.length > 0) {
        const firstModel = data.models[0];
        onModelSelect(firstModel.name);
        onModelCapabilitiesChange?.(firstModel.capabilities);
      }
    } catch (err) {
      console.error('Error fetching models:', err);
      setError('Failed to load Ollama models. Make sure Ollama is running.');
    } finally {
      setLoading(false);
    }
  };

  const formatModelSize = (size: number): string => {
    const gb = size / (1024 * 1024 * 1024);
    return `${gb.toFixed(1)}GB`;
  };

  const getModelFamily = (model: OllamaModel): string => {
    if (model.details?.family) {
      return model.details.family;
    }
    return model.name.split(':')[0];
  };

  const getCapabilityIcon = (capability: string) => {
    switch (capability) {
      case 'tools': return <ToolsIcon fontSize="small" />;
      case 'vision': return <VisionIcon fontSize="small" />;
      case 'code': return <CodeIcon fontSize="small" />;
      default: return null;
    }
  };

  const getCapabilityColor = (capability: string) => {
    switch (capability) {
      case 'tools': return 'primary';
      case 'vision': return 'secondary';
      case 'code': return 'success';
      default: return 'default';
    }
  };

  const handleModelChange = (modelName: string) => {
    const model = models.find(m => m.name === modelName);
    onModelSelect(modelName);
    if (model) {
      onModelCapabilitiesChange?.(model.capabilities);
    }
  };

  const selectedModelData = models.find(m => m.name === selectedModel);

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card sx={{ p: 3, textAlign: 'center' }}>
          <CircularProgress size={40} sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Discovering Models
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Connecting to Ollama and loading available models...
          </Typography>
        </Card>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Alert severity="error" sx={{ borderRadius: 3 }}>
          <Typography variant="h6" gutterBottom>
            Connection Error
          </Typography>
          {error}
        </Alert>
      </motion.div>
    );
  }

  if (models.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Alert severity="warning" sx={{ borderRadius: 3 }}>
          <Typography variant="h6" gutterBottom>
            No Models Found
          </Typography>
          No Ollama models found. Install a model using: <code>ollama pull llama3.2</code>
        </Alert>
      </motion.div>
    );
  }

  return (
    <Box>
      <FormControl fullWidth size="small">
        <InputLabel sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
          Model
        </InputLabel>
        <Select
          value={selectedModel || ''}
          label="Model"
          onChange={(e) => handleModelChange(e.target.value)}
          renderValue={(value) => {
            const model = models.find(m => m.name === value);
            if (!model) return value;

            return (
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" sx={{ flex: 1 }}>
                  {model.name}
                </Typography>
                <Box display="flex" gap={0.5}>
                  {model.capabilities.supportsTools && (
                    <Box sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      bgcolor: 'white',
                      opacity: 0.7
                    }} />
                  )}
                  {model.capabilities.supportsVision && (
                    <Box sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      bgcolor: 'white',
                      opacity: 0.5
                    }} />
                  )}
                  {model.capabilities.supportsCode && (
                    <Box sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      bgcolor: 'white',
                      opacity: 0.3
                    }} />
                  )}
                </Box>
              </Box>
            );
          }}
        >
            {models.map((model) => (
              <MenuItem key={model.name} value={model.name}>
                <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                  <Box>
                    <Typography variant="body2" fontWeight={500}>
                      {model.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatModelSize(model.size)} • {getModelFamily(model)}
                    </Typography>
                  </Box>
                  <Box display="flex" gap={0.5}>
                    {model.capabilities.supportsTools && (
                      <Tooltip title="Tools">
                        <Box sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: 'white',
                          opacity: 0.8
                        }} />
                      </Tooltip>
                    )}
                    {model.capabilities.supportsVision && (
                      <Tooltip title="Vision">
                        <Box sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: 'white',
                          opacity: 0.6
                        }} />
                      </Tooltip>
                    )}
                    {model.capabilities.supportsCode && (
                      <Tooltip title="Code">
                        <Box sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: 'white',
                          opacity: 0.4
                        }} />
                      </Tooltip>
                    )}
                  </Box>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
  );
}
