'use client';

import React, { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Avatar,
  Tooltip,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Visibility as VisionIcon,
  Build as ToolsIcon,
  Code as CodeIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { OllamaModel } from '@/types';

interface ModelSelectorProps {
  selectedModel: string | null;
  onModelSelect: (model: string) => void;
  onModelCapabilitiesChange?: (capabilities: any) => void;
}

export default function ModelSelector({
  selectedModel,
  onModelSelect,
  onModelCapabilitiesChange
}: ModelSelectorProps) {
  const [models, setModels] = useState<OllamaModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/ollama');
      
      if (!response.ok) {
        throw new Error('Failed to fetch models');
      }

      const data = await response.json();
      setModels(data.models || []);

      // Auto-select first model if none selected
      if (!selectedModel && data.models && data.models.length > 0) {
        const firstModel = data.models[0];
        onModelSelect(firstModel.name);
        onModelCapabilitiesChange?.(firstModel.capabilities);
      }
    } catch (err) {
      console.error('Error fetching models:', err);
      setError('Failed to load Ollama models. Make sure Ollama is running.');
    } finally {
      setLoading(false);
    }
  };

  const formatModelSize = (size: number): string => {
    const gb = size / (1024 * 1024 * 1024);
    return `${gb.toFixed(1)}GB`;
  };

  const getModelFamily = (model: OllamaModel): string => {
    if (model.details?.family) {
      return model.details.family;
    }
    return model.name.split(':')[0];
  };

  const getCapabilityIcon = (capability: string) => {
    switch (capability) {
      case 'tools': return <ToolsIcon fontSize="small" />;
      case 'vision': return <VisionIcon fontSize="small" />;
      case 'code': return <CodeIcon fontSize="small" />;
      default: return null;
    }
  };

  const getCapabilityColor = (capability: string) => {
    switch (capability) {
      case 'tools': return 'primary';
      case 'vision': return 'secondary';
      case 'code': return 'success';
      default: return 'default';
    }
  };

  const handleModelChange = (modelName: string) => {
    const model = models.find(m => m.name === modelName);
    onModelSelect(modelName);
    if (model) {
      onModelCapabilitiesChange?.(model.capabilities);
    }
  };

  const selectedModelData = models.find(m => m.name === selectedModel);

  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card sx={{ p: 3, textAlign: 'center' }}>
          <CircularProgress size={40} sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Discovering Models
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Connecting to Ollama and loading available models...
          </Typography>
        </Card>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Alert severity="error" sx={{ borderRadius: 3 }}>
          <Typography variant="h6" gutterBottom>
            Connection Error
          </Typography>
          {error}
        </Alert>
      </motion.div>
    );
  }

  if (models.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Alert severity="warning" sx={{ borderRadius: 3 }}>
          <Typography variant="h6" gutterBottom>
            No Models Found
          </Typography>
          No Ollama models found. Install a model using: <code>ollama pull llama3.2</code>
        </Alert>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <Box sx={{ mb: 3 }}>
        {/* Model Selection */}
        <FormControl fullWidth>
          <InputLabel id="model-select-label" sx={{ color: 'white' }}>
            Select AI Model
          </InputLabel>
          <Select
            labelId="model-select-label"
            value={selectedModel || ''}
            label="Select AI Model"
            onChange={(e) => handleModelChange(e.target.value)}
            sx={{
              '& .MuiSelect-select': {
                display: 'flex',
                alignItems: 'center',
                gap: 2,
              },
            }}
            renderValue={(value) => {
              const model = models.find(m => m.name === value);
              if (!model) return value;

              return (
                <Box display="flex" alignItems="center" gap={2} width="100%">
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      background: 'linear-gradient(135deg, #6366f1 0%, #ec4899 100%)',
                    }}
                  >
                    <BotIcon fontSize="small" />
                  </Avatar>
                  <Box flex={1}>
                    <Typography variant="body1" fontWeight={600}>
                      {model.name}
                    </Typography>
                    <Box display="flex" gap={1} mt={0.5}>
                      <Chip
                        label={formatModelSize(model.size)}
                        size="small"
                        sx={{ height: 18, fontSize: '0.7rem' }}
                      />
                      {model.capabilities.supportsTools && (
                        <Chip
                          icon={<ToolsIcon />}
                          label="Tools"
                          size="small"
                          color="primary"
                          sx={{ height: 18, fontSize: '0.7rem' }}
                        />
                      )}
                      {model.capabilities.supportsVision && (
                        <Chip
                          icon={<VisionIcon />}
                          label="Vision"
                          size="small"
                          color="secondary"
                          sx={{ height: 18, fontSize: '0.7rem' }}
                        />
                      )}
                      {model.capabilities.supportsCode && (
                        <Chip
                          icon={<CodeIcon />}
                          label="Code"
                          size="small"
                          color="success"
                          sx={{ height: 18, fontSize: '0.7rem' }}
                        />
                      )}
                    </Box>
                  </Box>
                </Box>
              );
            }}
          >
            {models.map((model, index) => (
              <MenuItem
                key={model.name}
                value={model.name}
                sx={{ py: 2 }}
              >
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  style={{ width: '100%' }}
                >
                  <Box display="flex" alignItems="center" gap={2} width="100%">
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        background: model.name === selectedModel
                          ? 'linear-gradient(135deg, #6366f1 0%, #ec4899 100%)'
                          : 'rgba(255, 255, 255, 0.1)',
                      }}
                    >
                      <BotIcon />
                    </Avatar>
                    <Box flex={1}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="subtitle1" fontWeight={600}>
                          {model.name}
                        </Typography>
                        <Chip
                          label={formatModelSize(model.size)}
                          size="small"
                          sx={{ fontSize: '0.7rem' }}
                        />
                      </Box>

                      <Box display="flex" gap={1} mb={1}>
                        <Chip
                          label={getModelFamily(model)}
                          size="small"
                          variant="outlined"
                          sx={{ height: 20, fontSize: '0.7rem' }}
                        />
                        {model.details?.parameter_size && (
                          <Chip
                            icon={<MemoryIcon />}
                            label={model.details.parameter_size}
                            size="small"
                            variant="outlined"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                        )}
                        {model.capabilities.contextLength && (
                          <Chip
                            icon={<SpeedIcon />}
                            label={`${(model.capabilities.contextLength / 1000).toFixed(0)}K context`}
                            size="small"
                            variant="outlined"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                        )}
                      </Box>

                      <Box display="flex" gap={1}>
                        {model.capabilities.supportsTools && (
                          <Tooltip title="Supports function calling and tools">
                            <Chip
                              icon={<ToolsIcon />}
                              label="Tools"
                              size="small"
                              color="primary"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          </Tooltip>
                        )}
                        {model.capabilities.supportsVision && (
                          <Tooltip title="Can process images and visual content">
                            <Chip
                              icon={<VisionIcon />}
                              label="Vision"
                              size="small"
                              color="secondary"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          </Tooltip>
                        )}
                        {model.capabilities.supportsCode && (
                          <Tooltip title="Optimized for code generation and analysis">
                            <Chip
                              icon={<CodeIcon />}
                              label="Code"
                              size="small"
                              color="success"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          </Tooltip>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </motion.div>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Selected Model Details */}
        <AnimatePresence>
          {selectedModelData && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card sx={{ mt: 2, p: 2 }}>
                <CardContent sx={{ p: 0 }}>
                  <Typography variant="h6" gutterBottom>
                    Model Capabilities
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color="primary.main" fontWeight={700}>
                          {formatModelSize(selectedModelData.size)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Model Size
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color="secondary.main" fontWeight={700}>
                          {selectedModelData.capabilities.contextLength ?
                            `${(selectedModelData.capabilities.contextLength / 1000).toFixed(0)}K` :
                            '4K'
                          }
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Context Length
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color="success.main" fontWeight={700}>
                          {Object.values(selectedModelData.capabilities).filter(Boolean).length}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Capabilities
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Box textAlign="center">
                        <Typography variant="h4" color="warning.main" fontWeight={700}>
                          {getModelFamily(selectedModelData)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Model Family
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </Box>
    </motion.div>
  );
}
