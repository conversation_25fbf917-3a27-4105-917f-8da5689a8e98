import { NextRequest, NextResponse } from 'next/server';
import { OllamaModel, OllamaResponse, ChatMessage, ModelCapabilities } from '@/types';

const OLLAMA_BASE_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

// Function to detect model capabilities based on name and family
function detectModelCapabilities(model: any): ModelCapabilities {
  const modelName = model.name.toLowerCase();
  const family = model.details?.family?.toLowerCase() || '';

  // Models known to support tools
  const toolSupportedModels = [
    'llama3.1', 'llama3.2', 'llama3.3', 'qwen2.5', 'qwen2', 'mistral', 'mixtral',
    'granite3', 'granite', 'phi3', 'phi4', 'gemma2', 'codellama', 'deepseek',
    'solar', 'yi', 'dolphin', 'orca', 'wizard', 'vicuna', 'nous', 'openchat'
  ];

  // Models known to support vision
  const visionSupportedModels = [
    'llava', 'llava-llama3', 'llava-phi3', 'llava-vicuna', 'bakllava',
    'moondream', 'minicpm-v', 'qwen2-vl', 'internvl', 'cogvlm', 'yi-vl'
  ];

  // Models known for code
  const codeSupportedModels = [
    'codellama', 'codegemma', 'starcoder', 'deepseek-coder', 'phind-codellama',
    'wizard-coder', 'magicoder', 'code-llama'
  ];

  const supportsTools = toolSupportedModels.some(supported =>
    modelName.includes(supported) || family.includes(supported)
  );

  const supportsVision = visionSupportedModels.some(supported =>
    modelName.includes(supported) || family.includes(supported)
  ) || modelName.includes('vision') || modelName.includes('visual');

  const supportsCode = codeSupportedModels.some(supported =>
    modelName.includes(supported) || family.includes(supported)
  ) || modelName.includes('code');

  return {
    supportsTools,
    supportsVision,
    supportsCode,
    contextLength: getContextLength(modelName, family),
  };
}

function getContextLength(modelName: string, family: string): number {
  // Common context lengths based on model families
  if (modelName.includes('128k') || family.includes('granite')) return 128000;
  if (modelName.includes('32k')) return 32000;
  if (modelName.includes('16k')) return 16000;
  if (modelName.includes('8k')) return 8000;
  if (family.includes('llama3')) return 8192;
  if (family.includes('qwen')) return 32768;
  if (family.includes('mistral')) return 32768;
  return 4096; // Default
}

// GET /api/ollama - Fetch available models
export async function GET() {
  try {
    const response = await fetch(`${OLLAMA_BASE_URL}/api/tags`);
    
    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status}`);
    }

    const data = await response.json();
    const models: OllamaModel[] = (data.models || []).map((model: any) => ({
      ...model,
      capabilities: detectModelCapabilities(model),
    }));

    return NextResponse.json({ models });
  } catch (error) {
    console.error('Error fetching Ollama models:', error);
    return NextResponse.json(
      { error: 'Failed to fetch models from Ollama' },
      { status: 500 }
    );
  }
}

// POST /api/ollama - Send chat message
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { model, messages, tools, hasVisionContent } = body;

    if (!model) {
      return NextResponse.json(
        { error: 'Model is required' },
        { status: 400 }
      );
    }

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    // Format messages for Ollama API
    const formattedMessages = messages.map((msg: ChatMessage) => ({
      role: msg.role,
      content: msg.content,
      ...(msg.toolCall && { tool_calls: [msg.toolCall] }),
    }));

    // Get model capabilities to determine what features to include
    const modelsResponse = await fetch(`${OLLAMA_BASE_URL}/api/tags`);
    let modelCapabilities: ModelCapabilities = { supportsTools: false, supportsVision: false, supportsCode: false };

    if (modelsResponse.ok) {
      const modelsData = await modelsResponse.json();
      const modelInfo = modelsData.models?.find((m: any) => m.name === model);
      if (modelInfo) {
        modelCapabilities = detectModelCapabilities(modelInfo);
      }
    }

    // Prepare the request payload
    const payload: any = {
      model,
      messages: formattedMessages,
      stream: false,
    };

    // Only include tools if the model supports them and tools are provided
    if (tools && tools.length > 0 && modelCapabilities.supportsTools) {
      payload.tools = tools;
    }

    // Validate vision content for non-vision models
    if (hasVisionContent && !modelCapabilities.supportsVision) {
      return NextResponse.json(
        { error: `Model ${model} does not support vision/image processing. Please use a vision-capable model like llava or qwen2-vl.` },
        { status: 400 }
      );
    }

    // Make request to Ollama
    const response = await fetch(`${OLLAMA_BASE_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Ollama API error:', errorText);
      throw new Error(`Ollama API error: ${response.status}`);
    }

    const data: OllamaResponse = await response.json();

    // Return the response
    return NextResponse.json({
      message: data.message,
      model: data.model,
      created_at: data.created_at,
      done: data.done,
    });

  } catch (error) {
    console.error('Error in Ollama chat:', error);
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    );
  }
}
