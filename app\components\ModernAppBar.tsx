'use client';

import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Button,
  Box,
  Chip,
  Tooltip,
  useTheme,
  useMediaQuery,
  Avatar,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  SmartToy as BotIcon,
  Circle as CircleIcon,
} from '@mui/icons-material';

interface ModernAppBarProps {
  onMenuClick: () => void;
  onSettingsClick: () => void;
  onRefreshClick: () => void;
  selectedModel: string | null;
  serverCount: number;
  toolCount: number;
  isConnected: boolean;
}

export default function ModernAppBar({
  onMenuClick,
  onSettingsClick,
  onRefreshClick,
  selectedModel,
  serverCount,
  toolCount,
  isConnected,
}: ModernAppBarProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <AppBar
      position="static"
      elevation={0}
      sx={{
        bgcolor: 'background.paper',
        borderBottom: 1,
        borderColor: 'divider',
        color: 'text.primary',
      }}
    >
      <Toolbar sx={{ px: { xs: 2, sm: 3 } }}>
        {/* Menu Button */}
        <IconButton
          edge="start"
          onClick={onMenuClick}
          sx={{
            mr: 2,
            color: 'text.primary',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <MenuIcon />
        </IconButton>

        {/* Logo and Title */}
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: 'primary.main',
              mr: 1.5,
            }}
          >
            <BotIcon fontSize="small" />
          </Avatar>
          <Box>
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                fontSize: '1.1rem',
                lineHeight: 1.2,
              }}
            >
              Ollama MCP Chat
            </Typography>
            {!isMobile && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ fontSize: '0.75rem' }}
              >
                AI Assistant with Tool Integration
              </Typography>
            )}
          </Box>
        </Box>

        {/* Spacer */}
        <Box sx={{ flexGrow: 1 }} />

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Connection Status */}
          <Tooltip title={isConnected ? 'Connected to Ollama' : 'Disconnected from Ollama'}>
            <Chip
              icon={
                <CircleIcon
                  sx={{
                    fontSize: '0.75rem !important',
                    color: isConnected ? 'success.main' : 'error.main',
                  }}
                />
              }
              label={isConnected ? 'Online' : 'Offline'}
              size="small"
              variant="outlined"
              sx={{
                height: 24,
                fontSize: '0.75rem',
                borderColor: isConnected ? 'success.main' : 'error.main',
                color: isConnected ? 'success.main' : 'error.main',
                display: { xs: 'none', sm: 'flex' },
              }}
            />
          </Tooltip>

          {/* Model Status */}
          {selectedModel && (
            <Tooltip title={`Active model: ${selectedModel}`}>
              <Chip
                label={isMobile ? selectedModel.split(':')[0] : selectedModel}
                size="small"
                color="primary"
                variant="outlined"
                sx={{
                  height: 24,
                  fontSize: '0.75rem',
                  maxWidth: { xs: 100, sm: 200 },
                }}
              />
            </Tooltip>
          )}

          {/* MCP Status */}
          {!isMobile && (
            <Tooltip
              title={`${serverCount} MCP server(s) configured, ${toolCount} tool(s) available`}
            >
              <Chip
                label={`${serverCount} servers, ${toolCount} tools`}
                size="small"
                color={serverCount > 0 ? 'success' : 'default'}
                variant="outlined"
                sx={{
                  height: 24,
                  fontSize: '0.75rem',
                }}
              />
            </Tooltip>
          )}
        </Box>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
          <Tooltip title="Refresh MCP configuration">
            <IconButton
              onClick={onRefreshClick}
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'action.hover',
                  color: 'primary.main',
                },
              }}
            >
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Configure MCP servers">
            <IconButton
              onClick={onSettingsClick}
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'action.hover',
                  color: 'primary.main',
                },
              }}
            >
              <SettingsIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Toolbar>
    </AppBar>
  );
}
