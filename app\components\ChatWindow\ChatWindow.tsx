'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
} from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import MessageBubble from './MessageBubble';
import InputBar from './InputBar';
import { ChatMessage, FileAttachment, Tool, ToolCall } from '@/types';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  messages: ChatMessage[];
}

interface ChatWindowProps {
  selectedModel: string | null;
  availableTools: Tool[];
  conversation: Conversation | null;
  onMessageSent: (message: ChatMessage) => void;
  modelCapabilities?: any;
}

export default function ChatWindow({
  selectedModel,
  availableTools,
  conversation,
  onMessageSent,
  modelCapabilities
}: ChatWindowProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const messages = conversation?.messages || [];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (content: string, attachments?: FileAttachment[]) => {
    if (!selectedModel) {
      setError('Please select a model first');
      return;
    }

    // Create user message
    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content,
      timestamp: new Date(),
      attachments,
    };

    onMessageSent(userMessage);
    setLoading(true);
    setError(null);

    try {
      // Check if message has vision content
      const hasVisionContent = attachments?.some(att => att.type === 'image');

      // Validate vision capability
      if (hasVisionContent && !modelCapabilities?.supportsVision) {
        setError(`Model ${selectedModel} does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.`);
        setLoading(false);
        return;
      }

      // Prepare messages for API
      const conversationMessages = [...messages, userMessage];

      // Only include tools if model supports them
      const toolsToSend = modelCapabilities?.supportsTools ? availableTools : [];

      // Send to Ollama API
      const response = await fetch('/api/ollama', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: conversationMessages.map(msg => ({
            role: msg.role,
            content: msg.content,
            ...(msg.toolCall && { tool_calls: [msg.toolCall] }),
          })),
          tools: toolsToSend,
          hasVisionContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response from Ollama');
      }

      const data = await response.json();
      const assistantMessage = data.message;

      // Create assistant message
      const newAssistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: assistantMessage.content,
        timestamp: new Date(),
      };

      // Check if there are tool calls (only if model supports tools)
      if (modelCapabilities?.supportsTools &&
          assistantMessage.tool_calls &&
          assistantMessage.tool_calls.length > 0) {
        const toolCall = assistantMessage.tool_calls[0];
        newAssistantMessage.toolCall = toolCall;

        // Add the assistant message with tool call
        onMessageSent(newAssistantMessage);

        // Execute the tool call
        await executeToolCall(toolCall, [...messages, userMessage, newAssistantMessage]);
      } else {
        // Add the assistant message
        onMessageSent(newAssistantMessage);
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const executeToolCall = async (toolCall: ToolCall, currentMessages: ChatMessage[]) => {
    try {
      // Parse tool arguments
      const toolArgs = JSON.parse(toolCall.function.arguments);

      // Execute tool via MCP API
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'execute_tool',
          serverName: 'exa', // For now, assume exa server
          toolName: toolCall.function.name,
          arguments: toolArgs,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to execute tool');
      }

      const toolResult = await response.json();

      // Create tool result message
      const toolResultMessage: ChatMessage = {
        id: uuidv4(),
        role: 'tool',
        content: JSON.stringify(toolResult.result, null, 2),
        timestamp: new Date(),
        toolResult: {
          toolCallId: toolCall.id,
          result: toolResult.result,
        },
      };

      onMessageSent(toolResultMessage);

      // Send the tool result back to Ollama for final response
      const finalResponse = await fetch('/api/ollama', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [...currentMessages, toolResultMessage].map(msg => ({
            role: msg.role,
            content: msg.content,
            ...(msg.toolCall && { tool_calls: [msg.toolCall] }),
          })),
          tools: availableTools,
        }),
      });

      if (finalResponse.ok) {
        const finalData = await finalResponse.json();
        const finalMessage: ChatMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: finalData.message.content,
          timestamp: new Date(),
        };

        onMessageSent(finalMessage);
      }
    } catch (err) {
      console.error('Error executing tool call:', err);
      
      // Add error message
      const errorMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Sorry, I encountered an error while using the tool. Please try again.',
        timestamp: new Date(),
      };

      onMessageSent(errorMessage);
    }
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      height="100%"
      sx={{ bgcolor: 'background.default' }}
    >
      {/* Error Alert */}
      {error && (
        <Alert severity="error" onClose={() => setError(null)} sx={{ m: 2 }}>
          {error}
        </Alert>
      )}

      {/* Messages Area */}
      <Box
        flex={1}
        overflow="auto"
        sx={{
          px: 2,
          py: 1,
        }}
      >
        <Container maxWidth="md">
          {messages.length === 0 ? (
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              height="100%"
              textAlign="center"
            >
              <Typography variant="h5" color="text.secondary" gutterBottom>
                {conversation ? 'Ready to Chat' : 'Welcome to Ollama MCP Chat'}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {conversation
                  ? 'Continue your conversation or start a new message.'
                  : 'Create a new conversation to start chatting with your selected model.'
                }
                {availableTools.length > 0 && (
                  <> The assistant has access to {availableTools.length} tool(s).</>
                )}
              </Typography>
            </Box>
          ) : (
            messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                isUser={message.role === 'user'}
              />
            ))
          )}
          <div ref={messagesEndRef} />
        </Container>
      </Box>

      {/* Input Area */}
      <Box sx={{ p: 2 }}>
        <Container maxWidth="md">
          <InputBar
            onSendMessage={handleSendMessage}
            disabled={!selectedModel || !conversation}
            loading={loading}
            modelCapabilities={modelCapabilities}
          />
        </Container>
      </Box>
    </Box>
  );
}
