'use client';

import React, { useState, useRef } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Button,
  Paper,
  Chip,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Send as SendIcon,
  AttachFile as AttachFileIcon,
  Image as ImageIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { FileAttachment } from '@/types';

interface InputBarProps {
  onSendMessage: (content: string, attachments?: FileAttachment[]) => void;
  disabled?: boolean;
  loading?: boolean;
  modelCapabilities?: any;
}

export default function InputBar({ onSendMessage, disabled, loading, modelCapabilities }: InputBarProps) {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<FileAttachment[]>([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSend = () => {
    if (message.trim() || attachments.length > 0) {
      onSendMessage(message.trim(), attachments);
      setMessage('');
      setAttachments([]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Check for image files and vision capability
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    if (imageFiles.length > 0 && !modelCapabilities?.supportsVision) {
      alert('The selected model does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl to upload images.');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    setUploading(true);

    try {
      const uploadPromises = files.map(async (file) => {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const data = await response.json();
        return data.file as FileAttachment;
      });

      const uploadedFiles = await Promise.all(uploadPromises);
      setAttachments(prev => [...prev, ...uploadedFiles]);
    } catch (error) {
      console.error('Error uploading files:', error);
      // TODO: Show error toast
    } finally {
      setUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeAttachment = (attachmentId: string) => {
    setAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Paper
      elevation={2}
      sx={{
        p: 2,
        borderRadius: 3,
        bgcolor: 'background.paper',
      }}
    >
      {/* File attachments preview */}
      {attachments.length > 0 && (
        <Box mb={2} display="flex" flexWrap="wrap" gap={1}>
          {attachments.map((attachment) => (
            <Chip
              key={attachment.id}
              icon={attachment.type === 'image' ? <ImageIcon /> : <AttachFileIcon />}
              label={`${attachment.name} (${formatFileSize(attachment.size)})`}
              onDelete={() => removeAttachment(attachment.id)}
              deleteIcon={<CloseIcon />}
              variant="outlined"
              size="small"
            />
          ))}
        </Box>
      )}

      <Box display="flex" alignItems="flex-end" gap={1}>
        {/* File upload button */}
        <Tooltip title={
          !modelCapabilities?.supportsVision
            ? "Image uploads require a vision-capable model"
            : "Attach files (images, documents)"
        }>
          <IconButton
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || uploading}
            sx={{ mb: 0.5 }}
          >
            {uploading ? (
              <CircularProgress size={20} />
            ) : (
              <AttachFileIcon />
            )}
          </IconButton>
        </Tooltip>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={
            modelCapabilities?.supportsVision
              ? "image/*,.pdf,.txt,.md,.doc,.docx"
              : ".pdf,.txt,.md,.doc,.docx"
          }
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />

        {/* Message input */}
        <TextField
          fullWidth
          multiline
          maxRows={4}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message..."
          variant="outlined"
          disabled={disabled}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
            },
          }}
        />

        {/* Send button */}
        <Button
          variant="contained"
          onClick={handleSend}
          disabled={disabled || loading || (!message.trim() && attachments.length === 0)}
          sx={{
            minWidth: 48,
            height: 48,
            borderRadius: 2,
            mb: 0.5,
          }}
        >
          {loading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            <SendIcon />
          )}
        </Button>
      </Box>
    </Paper>
  );
}
