"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ollama/route";
exports.ids = ["app/api/ollama/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_20021792_Documents_Projects_Chat_app_api_ollama_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ollama/route.ts */ \"(rsc)/./app/api/ollama/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ollama/route\",\n        pathname: \"/api/ollama\",\n        filename: \"route\",\n        bundlePath: \"app/api/ollama/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\api\\\\ollama\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_20021792_Documents_Projects_Chat_app_api_ollama_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/ollama/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/ollama/route.ts":
/*!*********************************!*\
  !*** ./app/api/ollama/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nconst OLLAMA_BASE_URL = \"http://localhost:11434\" || 0;\n// Function to detect model capabilities based on name and family\nfunction detectModelCapabilities(model) {\n    const modelName = model.name.toLowerCase();\n    const family = model.details?.family?.toLowerCase() || \"\";\n    // Models known to support tools\n    const toolSupportedModels = [\n        \"llama3.1\",\n        \"llama3.2\",\n        \"llama3.3\",\n        \"qwen2.5\",\n        \"qwen2\",\n        \"mistral\",\n        \"mixtral\",\n        \"granite3\",\n        \"granite\",\n        \"phi3\",\n        \"phi4\",\n        \"gemma2\",\n        \"codellama\",\n        \"deepseek\",\n        \"solar\",\n        \"yi\",\n        \"dolphin\",\n        \"orca\",\n        \"wizard\",\n        \"vicuna\",\n        \"nous\",\n        \"openchat\"\n    ];\n    // Models known to support vision\n    const visionSupportedModels = [\n        \"llava\",\n        \"llava-llama3\",\n        \"llava-phi3\",\n        \"llava-vicuna\",\n        \"bakllava\",\n        \"moondream\",\n        \"minicpm-v\",\n        \"qwen2-vl\",\n        \"internvl\",\n        \"cogvlm\",\n        \"yi-vl\"\n    ];\n    // Models known for code\n    const codeSupportedModels = [\n        \"codellama\",\n        \"codegemma\",\n        \"starcoder\",\n        \"deepseek-coder\",\n        \"phind-codellama\",\n        \"wizard-coder\",\n        \"magicoder\",\n        \"code-llama\"\n    ];\n    const supportsTools = toolSupportedModels.some((supported)=>modelName.includes(supported) || family.includes(supported));\n    const supportsVision = visionSupportedModels.some((supported)=>modelName.includes(supported) || family.includes(supported)) || modelName.includes(\"vision\") || modelName.includes(\"visual\");\n    const supportsCode = codeSupportedModels.some((supported)=>modelName.includes(supported) || family.includes(supported)) || modelName.includes(\"code\");\n    return {\n        supportsTools,\n        supportsVision,\n        supportsCode,\n        contextLength: getContextLength(modelName, family)\n    };\n}\nfunction getContextLength(modelName, family) {\n    // Common context lengths based on model families\n    if (modelName.includes(\"128k\") || family.includes(\"granite\")) return 128000;\n    if (modelName.includes(\"32k\")) return 32000;\n    if (modelName.includes(\"16k\")) return 16000;\n    if (modelName.includes(\"8k\")) return 8000;\n    if (family.includes(\"llama3\")) return 8192;\n    if (family.includes(\"qwen\")) return 32768;\n    if (family.includes(\"mistral\")) return 32768;\n    return 4096; // Default\n}\n// GET /api/ollama - Fetch available models\nasync function GET() {\n    try {\n        const response = await fetch(`${OLLAMA_BASE_URL}/api/tags`);\n        if (!response.ok) {\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        const models = (data.models || []).map((model)=>({\n                ...model,\n                capabilities: detectModelCapabilities(model)\n            }));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            models\n        });\n    } catch (error) {\n        console.error(\"Error fetching Ollama models:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch models from Ollama\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/ollama - Send chat message\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { model, messages, tools, hasVisionContent } = body;\n        if (!model) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Model is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (!messages || !Array.isArray(messages)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Messages array is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Format messages for Ollama API\n        const formattedMessages = messages.map((msg)=>({\n                role: msg.role,\n                content: msg.content,\n                ...msg.toolCall && {\n                    tool_calls: [\n                        msg.toolCall\n                    ]\n                }\n            }));\n        // Get model capabilities to determine what features to include\n        const modelsResponse = await fetch(`${OLLAMA_BASE_URL}/api/tags`);\n        let modelCapabilities = {\n            supportsTools: false,\n            supportsVision: false,\n            supportsCode: false\n        };\n        if (modelsResponse.ok) {\n            const modelsData = await modelsResponse.json();\n            const modelInfo = modelsData.models?.find((m)=>m.name === model);\n            if (modelInfo) {\n                modelCapabilities = detectModelCapabilities(modelInfo);\n            }\n        }\n        // Prepare the request payload\n        const payload = {\n            model,\n            messages: formattedMessages,\n            stream: false\n        };\n        // Only include tools if the model supports them and tools are provided\n        if (tools && tools.length > 0 && modelCapabilities.supportsTools) {\n            payload.tools = tools;\n        }\n        // Validate vision content for non-vision models\n        if (hasVisionContent && !modelCapabilities.supportsVision) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Model ${model} does not support vision/image processing. Please use a vision-capable model like llava or qwen2-vl.`\n            }, {\n                status: 400\n            });\n        }\n        // Make request to Ollama\n        const response = await fetch(`${OLLAMA_BASE_URL}/api/chat`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Ollama API error:\", errorText);\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        // Return the response\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: data.message,\n            model: data.model,\n            created_at: data.created_at,\n            done: data.done\n        });\n    } catch (error) {\n        console.error(\"Error in Ollama chat:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to process chat request\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ollama/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();