'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import ModelSelector from './components/ModelSelector';
import ServerConfigDialog from './components/ServerConfigDialog';
import ChatWindow from './components/ChatWindow/ChatWindow';
import ConversationSidebar from './components/ConversationSidebar';
import ModernAppBar from './components/ModernAppBar';
import { MCPConfig, Tool, ChatMessage } from './types';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  messages: ChatMessage[];
}

export default function HomePage() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [mcpConfig, setMcpConfig] = useState<MCPConfig>({ mcpServers: {} });
  const [availableTools, setAvailableTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [modelCapabilities, setModelCapabilities] = useState<any>(null);

  useEffect(() => {
    loadMCPConfig();
    loadConversations();
  }, []);

  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  const loadMCPConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/mcp');

      if (response.ok) {
        const data = await response.json();
        setMcpConfig({ mcpServers: data.mcpServers || {} });
        setAvailableTools(data.tools || []);
      }
    } catch (error) {
      console.error('Error loading MCP config:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadConversations = () => {
    // Load conversations from localStorage
    const saved = localStorage.getItem('ollama-chat-conversations');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setConversations(parsed.map((conv: any) => ({
          ...conv,
          timestamp: new Date(conv.timestamp),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        })));
      } catch (error) {
        console.error('Error loading conversations:', error);
      }
    }
  };

  const saveConversations = (convs: Conversation[]) => {
    localStorage.setItem('ollama-chat-conversations', JSON.stringify(convs));
  };

  const handleConfigUpdate = (newConfig: MCPConfig) => {
    setMcpConfig(newConfig);
    loadMCPConfig(); // Reload to get updated tools
  };

  const handleRefresh = () => {
    loadMCPConfig();
  };

  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: uuidv4(),
      title: 'New Conversation',
      lastMessage: '',
      timestamp: new Date(),
      messageCount: 0,
      messages: [],
    };

    const updatedConversations = [newConversation, ...conversations];
    setConversations(updatedConversations);
    setActiveConversationId(newConversation.id);
    saveConversations(updatedConversations);

    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const selectConversation = (id: string) => {
    setActiveConversationId(id);
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const deleteConversation = (id: string) => {
    const updatedConversations = conversations.filter(conv => conv.id !== id);
    setConversations(updatedConversations);
    saveConversations(updatedConversations);

    if (activeConversationId === id) {
      setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);
    }
  };

  const renameConversation = (id: string, newTitle: string) => {
    const updatedConversations = conversations.map(conv =>
      conv.id === id ? { ...conv, title: newTitle } : conv
    );
    setConversations(updatedConversations);
    saveConversations(updatedConversations);
  };

  const updateConversationWithMessage = (message: ChatMessage) => {
    const activeConv = conversations.find(conv => conv.id === activeConversationId);
    if (!activeConv) return;

    const updatedConversation = {
      ...activeConv,
      messages: [...activeConv.messages, message],
      lastMessage: message.content,
      timestamp: new Date(),
      messageCount: activeConv.messageCount + 1,
      title: activeConv.title === 'New Conversation' && message.role === 'user'
        ? message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '')
        : activeConv.title,
    };

    const updatedConversations = conversations.map(conv =>
      conv.id === activeConversationId ? updatedConversation : conv
    );

    setConversations(updatedConversations);
    saveConversations(updatedConversations);
  };

  const getActiveConversation = () => {
    return conversations.find(conv => conv.id === activeConversationId) || null;
  };

  const serverCount = Object.keys(mcpConfig.mcpServers).length;
  const isConnected = selectedModel !== null;

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Modern App Bar */}
      <ModernAppBar
        onMenuClick={() => setSidebarOpen(!sidebarOpen)}
        onSettingsClick={() => setConfigDialogOpen(true)}
        onRefreshClick={handleRefresh}
        selectedModel={selectedModel}
        serverCount={serverCount}
        toolCount={availableTools.length}
        isConnected={isConnected}
      />

      {/* Main Layout with Sidebar */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* Conversation Sidebar */}
        <ConversationSidebar
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          conversations={conversations}
          activeConversationId={activeConversationId}
          onConversationSelect={selectConversation}
          onNewConversation={createNewConversation}
          onDeleteConversation={deleteConversation}
          onRenameConversation={renameConversation}
        />

        {/* Main Content Area */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            ml: sidebarOpen && !isMobile ? 0 : 0,
            transition: theme.transitions.create(['margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
          }}
        >
          {/* Model Selection Header */}
          <Paper
            elevation={0}
            sx={{
              p: 2,
              borderBottom: 1,
              borderColor: 'divider',
              bgcolor: 'background.default',
            }}
          >
            <Container maxWidth="lg">
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={4}>
                  <ModelSelector
                    selectedModel={selectedModel}
                    onModelSelect={setSelectedModel}
                    onModelCapabilitiesChange={setModelCapabilities}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={8}>
                  <Box display="flex" alignItems="center" gap={1} justifyContent="flex-end">
                    {getActiveConversation() && (
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="body2" color="text.secondary">
                          {getActiveConversation()?.messageCount || 0} messages
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Container>
          </Paper>

          {/* Chat Window */}
          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            <ChatWindow
              selectedModel={selectedModel}
              availableTools={availableTools}
              conversation={getActiveConversation()}
              onMessageSent={updateConversationWithMessage}
              modelCapabilities={modelCapabilities}
            />
          </Box>
        </Box>
      </Box>

      {/* Server Configuration Dialog */}
      <ServerConfigDialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        onConfigUpdate={handleConfigUpdate}
      />
    </Box>
  );
}
