'use client';

import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Person as PersonIcon,
  SmartToy as BotIcon,
  Build as ToolIcon,
  Image as ImageIcon,
  Description as DocumentIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';
import { ChatMessage } from '@/types';

interface MessageBubbleProps {
  message: ChatMessage;
  isUser: boolean;
}

export default function MessageBubble({ message, isUser }: MessageBubbleProps) {
  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.content);
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(new Date(timestamp));
  };

  const renderAttachments = () => {
    if (!message.attachments || message.attachments.length === 0) {
      return null;
    }

    return (
      <Box display="flex" flexWrap="wrap" gap={1} mb={1}>
        {message.attachments.map((attachment) => (
          <Chip
            key={attachment.id}
            icon={attachment.type === 'image' ? <ImageIcon /> : <DocumentIcon />}
            label={attachment.name}
            size="small"
            variant="outlined"
            onClick={() => window.open(attachment.url, '_blank')}
            sx={{ cursor: 'pointer' }}
          />
        ))}
      </Box>
    );
  };

  const renderToolCall = () => {
    if (!message.toolCall) {
      return null;
    }

    const toolArgs = JSON.parse(message.toolCall.function.arguments);

    return (
      <Box mb={1}>
        <Chip
          icon={<ToolIcon />}
          label={`Tool: ${message.toolCall.function.name}`}
          size="small"
          color="secondary"
          variant="outlined"
        />
        <Box mt={1} p={1} bgcolor="grey.100" borderRadius={1}>
          <Typography variant="caption" color="text.secondary">
            Arguments:
          </Typography>
          <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
            {JSON.stringify(toolArgs, null, 2)}
          </Typography>
        </Box>
      </Box>
    );
  };

  const renderToolResult = () => {
    if (!message.toolResult) {
      return null;
    }

    return (
      <Box mb={1}>
        <Chip
          icon={<ToolIcon />}
          label="Tool Result"
          size="small"
          color="success"
          variant="outlined"
        />
        <Box mt={1} p={1} bgcolor="success.50" borderRadius={1}>
          <Typography variant="body2" component="pre" sx={{ fontSize: '0.875rem' }}>
            {typeof message.toolResult.result === 'string'
              ? message.toolResult.result
              : JSON.stringify(message.toolResult.result, null, 2)}
          </Typography>
        </Box>
      </Box>
    );
  };

  return (
    <Box
      display="flex"
      justifyContent={isUser ? 'flex-end' : 'flex-start'}
      mb={3}
    >
      <Box
        sx={{
          maxWidth: '80%',
          p: 2,
          borderRadius: 3,
          backdropFilter: 'blur(20px)',
          background: isUser
            ? 'rgba(255, 255, 255, 0.1)'
            : 'rgba(255, 255, 255, 0.05)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          position: 'relative',
          '&:hover .copy-button': {
            opacity: 1,
          },
        }}
      >
        {/* Copy button */}
        <IconButton
          className="copy-button"
          size="small"
          onClick={handleCopyMessage}
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            opacity: 0,
            transition: 'opacity 0.2s',
            color: 'rgba(255, 255, 255, 0.7)',
            '&:hover': {
              background: 'rgba(255, 255, 255, 0.1)',
            },
          }}
        >
          <CopyIcon fontSize="small" />
        </IconButton>

        {/* Attachments */}
        {renderAttachments()}

        {/* Tool Call */}
        {renderToolCall()}

        {/* Tool Result */}
        {renderToolResult()}

        {/* Message Content */}
        <Typography
          variant="body1"
          sx={{
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            pr: 5, // Space for copy button
            color: 'white',
          }}
        >
          {message.content}
        </Typography>

        {/* Timestamp */}
        <Typography
          variant="caption"
          sx={{
            display: 'block',
            mt: 1,
            color: 'rgba(255, 255, 255, 0.5)',
            textAlign: isUser ? 'right' : 'left',
          }}
        >
          {formatTimestamp(message.timestamp)}
        </Typography>
      </Box>
    </Box>
  );
}
