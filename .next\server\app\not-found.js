/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/AnimatedBackground.tsx */ \"(ssr)/./app/components/AnimatedBackground.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ThemeProvider.tsx */ \"(ssr)/./app/components/ThemeProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDMjAwMjE3OTIlNUNEb2N1bWVudHMlNUNQcm9qZWN0cyU1Q0NoYXQlNUNhcHAlNUNjb21wb25lbnRzJTVDQW5pbWF0ZWRCYWNrZ3JvdW5kLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1QzIwMDIxNzkyJTVDRG9jdW1lbnRzJTVDUHJvamVjdHMlNUNDaGF0JTVDYXBwJTVDY29tcG9uZW50cyU1Q1RoZW1lUHJvdmlkZXIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBNEg7QUFDNUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vbGxhbWEtbWNwLWNoYXQvP2ZhOGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxBbmltYXRlZEJhY2tncm91bmQudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFwyMDAyMTc5MlxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcQ2hhdFxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxUaGVtZVByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CAnimatedBackground.tsx&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp%5Ccomponents%5CThemeProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/AnimatedBackground.tsx":
/*!***********************************************!*\
  !*** ./app/components/AnimatedBackground.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimatedBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AnimatedBackground() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        sx: {\n            position: \"fixed\",\n            top: 0,\n            left: 0,\n            width: \"100%\",\n            height: \"100%\",\n            zIndex: -1,\n            overflow: \"hidden\",\n            background: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    top: \"10%\",\n                    left: \"10%\",\n                    width: \"300px\",\n                    height: \"300px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%)\",\n                    filter: \"blur(40px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        100,\n                        0\n                    ],\n                    y: [\n                        0,\n                        -50,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.2,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    top: \"60%\",\n                    right: \"10%\",\n                    width: \"400px\",\n                    height: \"400px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%)\",\n                    filter: \"blur(40px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        -80,\n                        0\n                    ],\n                    y: [\n                        0,\n                        60,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        0.8,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 25,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                style: {\n                    position: \"absolute\",\n                    bottom: \"20%\",\n                    left: \"30%\",\n                    width: \"250px\",\n                    height: \"250px\",\n                    borderRadius: \"50%\",\n                    background: \"radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%)\",\n                    filter: \"blur(30px)\"\n                },\n                animate: {\n                    x: [\n                        0,\n                        60,\n                        0\n                    ],\n                    y: [\n                        0,\n                        -40,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.3,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 18,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            Array.from({\n                length: 20\n            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    style: {\n                        position: \"absolute\",\n                        width: \"4px\",\n                        height: \"4px\",\n                        borderRadius: \"50%\",\n                        background: \"rgba(255, 255, 255, 0.2)\",\n                        left: `${Math.random() * 100}%`,\n                        top: `${Math.random() * 100}%`\n                    },\n                    animate: {\n                        y: [\n                            0,\n                            -100,\n                            0\n                        ],\n                        opacity: [\n                            0,\n                            1,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 3 + Math.random() * 2,\n                        repeat: Infinity,\n                        delay: Math.random() * 2,\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\AnimatedBackground.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/AnimatedBackground.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./app/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* harmony import */ var _theme_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../theme/theme */ \"(ssr)/./app/theme/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ThemeProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        theme: _theme_theme__WEBPACK_IMPORTED_MODULE_2__.theme,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ThemeProvider.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMEI7QUFDK0M7QUFDckI7QUFDYjtBQU14QixTQUFTQyxjQUFjLEVBQUVJLFFBQVEsRUFBc0I7SUFDcEUscUJBQ0UsOERBQUNILDREQUFnQkE7UUFBQ0UsT0FBT0EsK0NBQUtBOzswQkFDNUIsOERBQUNELGlFQUFXQTs7Ozs7WUFDWEU7Ozs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL29sbGFtYS1tY3AtY2hhdC8uL2FwcC9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4P2MyY2MiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBNdWlUaGVtZVByb3ZpZGVyIH0gZnJvbSAnQG11aS9tYXRlcmlhbC9zdHlsZXMnO1xuaW1wb3J0IENzc0Jhc2VsaW5lIGZyb20gJ0BtdWkvbWF0ZXJpYWwvQ3NzQmFzZWxpbmUnO1xuaW1wb3J0IHsgdGhlbWUgfSBmcm9tICcuLi90aGVtZS90aGVtZSc7XG5cbmludGVyZmFjZSBUaGVtZVByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE11aVRoZW1lUHJvdmlkZXIgdGhlbWU9e3RoZW1lfT5cbiAgICAgIDxDc3NCYXNlbGluZSAvPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTXVpVGhlbWVQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJNdWlUaGVtZVByb3ZpZGVyIiwiQ3NzQmFzZWxpbmUiLCJ0aGVtZSIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./app/theme/theme.ts":
/*!****************************!*\
  !*** ./app/theme/theme.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   theme: () => (/* binding */ theme)\n/* harmony export */ });\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/createTheme.js\");\n/* __next_internal_client_entry_do_not_use__ theme auto */ \n// Minimalist black and white glassmorphism palette\nconst palette = {\n    primary: {\n        main: \"#ffffff\",\n        light: \"#ffffff\",\n        dark: \"#f5f5f5\",\n        contrastText: \"#000000\",\n        50: \"#ffffff\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    secondary: {\n        main: \"#000000\",\n        light: \"#424242\",\n        dark: \"#000000\",\n        contrastText: \"#ffffff\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    success: {\n        main: \"#ffffff\",\n        light: \"#ffffff\",\n        dark: \"#f5f5f5\",\n        50: \"#ffffff\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    warning: {\n        main: \"#9e9e9e\",\n        light: \"#bdbdbd\",\n        dark: \"#757575\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    error: {\n        main: \"#757575\",\n        light: \"#9e9e9e\",\n        dark: \"#424242\",\n        50: \"#fafafa\",\n        100: \"#f5f5f5\",\n        200: \"#eeeeee\",\n        300: \"#e0e0e0\",\n        400: \"#bdbdbd\",\n        500: \"#9e9e9e\",\n        600: \"#757575\",\n        700: \"#616161\",\n        800: \"#424242\",\n        900: \"#212121\"\n    },\n    background: {\n        default: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\",\n        paper: \"rgba(255, 255, 255, 0.05)\",\n        glass: \"rgba(255, 255, 255, 0.03)\",\n        glassDark: \"rgba(0, 0, 0, 0.2)\"\n    },\n    text: {\n        primary: \"#ffffff\",\n        secondary: \"rgba(255, 255, 255, 0.7)\",\n        disabled: \"rgba(255, 255, 255, 0.3)\"\n    },\n    divider: \"rgba(255, 255, 255, 0.08)\",\n    action: {\n        hover: \"rgba(255, 255, 255, 0.05)\",\n        selected: \"rgba(255, 255, 255, 0.08)\",\n        disabled: \"rgba(255, 255, 255, 0.2)\",\n        disabledBackground: \"rgba(255, 255, 255, 0.05)\"\n    }\n};\n// Modern typography with better hierarchy\nconst typography = {\n    fontFamily: [\n        '\"Inter\"',\n        \"-apple-system\",\n        \"BlinkMacSystemFont\",\n        '\"Segoe UI\"',\n        \"Roboto\",\n        '\"Helvetica Neue\"',\n        \"Arial\",\n        \"sans-serif\"\n    ].join(\",\"),\n    h1: {\n        fontSize: \"2.5rem\",\n        fontWeight: 800,\n        lineHeight: 1.1,\n        letterSpacing: \"-0.025em\",\n        background: \"linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%)\",\n        WebkitBackgroundClip: \"text\",\n        WebkitTextFillColor: \"transparent\",\n        backgroundClip: \"text\"\n    },\n    h2: {\n        fontSize: \"2rem\",\n        fontWeight: 700,\n        lineHeight: 1.2,\n        letterSpacing: \"-0.025em\"\n    },\n    h3: {\n        fontSize: \"1.75rem\",\n        fontWeight: 600,\n        lineHeight: 1.3,\n        letterSpacing: \"-0.015em\"\n    },\n    h4: {\n        fontSize: \"1.5rem\",\n        fontWeight: 600,\n        lineHeight: 1.3,\n        letterSpacing: \"-0.015em\"\n    },\n    h5: {\n        fontSize: \"1.25rem\",\n        fontWeight: 600,\n        lineHeight: 1.4,\n        letterSpacing: \"-0.01em\"\n    },\n    h6: {\n        fontSize: \"1.125rem\",\n        fontWeight: 600,\n        lineHeight: 1.4,\n        letterSpacing: \"-0.01em\"\n    },\n    body1: {\n        fontSize: \"1rem\",\n        lineHeight: 1.6,\n        fontWeight: 400\n    },\n    body2: {\n        fontSize: \"0.875rem\",\n        lineHeight: 1.5,\n        fontWeight: 400\n    },\n    caption: {\n        fontSize: \"0.75rem\",\n        lineHeight: 1.4,\n        fontWeight: 500,\n        letterSpacing: \"0.025em\",\n        textTransform: \"uppercase\"\n    },\n    button: {\n        fontWeight: 600,\n        letterSpacing: \"0.025em\",\n        textTransform: \"none\"\n    }\n};\n// Custom spacing and breakpoints\nconst spacing = 8;\nconst breakpoints = {\n    values: {\n        xs: 0,\n        sm: 640,\n        md: 768,\n        lg: 1024,\n        xl: 1280\n    }\n};\n// Modern glassmorphism component overrides\nconst components = {\n    MuiCssBaseline: {\n        styleOverrides: {\n            \"*\": {\n                boxSizing: \"border-box\"\n            },\n            html: {\n                height: \"100%\"\n            },\n            body: {\n                height: \"100%\",\n                margin: 0,\n                padding: 0,\n                background: \"linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)\",\n                backgroundAttachment: \"fixed\",\n                scrollbarWidth: \"thin\",\n                scrollbarColor: \"rgba(255, 255, 255, 0.2) transparent\",\n                \"&::-webkit-scrollbar\": {\n                    width: \"6px\"\n                },\n                \"&::-webkit-scrollbar-track\": {\n                    background: \"transparent\"\n                },\n                \"&::-webkit-scrollbar-thumb\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    borderRadius: \"10px\",\n                    border: \"none\"\n                },\n                \"&::-webkit-scrollbar-thumb:hover\": {\n                    background: \"rgba(255, 255, 255, 0.3)\"\n                }\n            },\n            \"#__next\": {\n                height: \"100%\"\n            }\n        }\n    },\n    MuiButton: {\n        styleOverrides: {\n            root: {\n                textTransform: \"none\",\n                borderRadius: \"16px\",\n                fontWeight: 600,\n                padding: \"12px 24px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.05)\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                color: \"white\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    transform: \"translateY(-1px)\",\n                    boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.3)\"\n                },\n                \"&:active\": {\n                    transform: \"translateY(0px)\"\n                }\n            },\n            contained: {\n                background: \"linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%)\",\n                border: \"none\",\n                color: \"#000000\",\n                boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.2)\",\n                \"&:hover\": {\n                    background: \"linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)\",\n                    boxShadow: \"0 15px 40px rgba(0, 0, 0, 0.3)\"\n                }\n            },\n            outlined: {\n                borderColor: \"rgba(255, 255, 255, 0.2)\",\n                \"&:hover\": {\n                    borderColor: \"rgba(255, 255, 255, 0.3)\",\n                    background: \"rgba(255, 255, 255, 0.05)\"\n                }\n            }\n        }\n    },\n    MuiPaper: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"24px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                boxShadow: \"0 20px 40px rgba(0, 0, 0, 0.1)\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.15)\",\n                    transform: \"translateY(-2px)\",\n                    boxShadow: \"0 30px 60px rgba(0, 0, 0, 0.15)\"\n                }\n            }\n        }\n    },\n    MuiTextField: {\n        styleOverrides: {\n            root: {\n                \"& .MuiOutlinedInput-root\": {\n                    borderRadius: \"16px\",\n                    backdropFilter: \"blur(20px)\",\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                    color: \"white\",\n                    transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                    \"& fieldset\": {\n                        border: \"none\"\n                    },\n                    \"&:hover\": {\n                        background: \"rgba(255, 255, 255, 0.15)\",\n                        transform: \"translateY(-1px)\"\n                    },\n                    \"&.Mui-focused\": {\n                        background: \"rgba(255, 255, 255, 0.2)\",\n                        boxShadow: \"0 0 0 2px rgba(99, 102, 241, 0.5)\"\n                    }\n                },\n                \"& .MuiInputLabel-root\": {\n                    color: \"rgba(255, 255, 255, 0.7)\",\n                    \"&.Mui-focused\": {\n                        color: \"#6366f1\"\n                    }\n                },\n                \"& .MuiOutlinedInput-input\": {\n                    color: \"white\",\n                    \"&::placeholder\": {\n                        color: \"rgba(255, 255, 255, 0.5)\",\n                        opacity: 1\n                    }\n                }\n            }\n        }\n    },\n    MuiChip: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"12px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                color: \"white\",\n                fontWeight: 500,\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    transform: \"translateY(-1px)\"\n                }\n            }\n        }\n    },\n    MuiIconButton: {\n        styleOverrides: {\n            root: {\n                borderRadius: \"12px\",\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                color: \"white\",\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n                \"&:hover\": {\n                    background: \"rgba(255, 255, 255, 0.2)\",\n                    transform: \"translateY(-2px)\",\n                    boxShadow: \"0 10px 20px rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        }\n    },\n    MuiDrawer: {\n        styleOverrides: {\n            paper: {\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.05)\",\n                border: \"1px solid rgba(255, 255, 255, 0.1)\",\n                borderLeft: \"none\"\n            }\n        }\n    },\n    MuiAppBar: {\n        styleOverrides: {\n            root: {\n                backdropFilter: \"blur(20px)\",\n                background: \"rgba(255, 255, 255, 0.1)\",\n                border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                borderTop: \"none\",\n                borderLeft: \"none\",\n                borderRight: \"none\",\n                boxShadow: \"0 10px 30px rgba(0, 0, 0, 0.1)\"\n            }\n        }\n    }\n};\n// Create the theme\nconst themeOptions = {\n    palette,\n    typography,\n    spacing,\n    breakpoints,\n    components,\n    transitions: {\n        duration: {\n            shortest: 150,\n            shorter: 200,\n            short: 250,\n            standard: 300,\n            complex: 375,\n            enteringScreen: 225,\n            leavingScreen: 195\n        },\n        easing: {\n            easeInOut: \"cubic-bezier(0.4, 0, 0.2, 1)\",\n            easeOut: \"cubic-bezier(0.0, 0, 0.2, 1)\",\n            easeIn: \"cubic-bezier(0.4, 0, 1, 1)\",\n            sharp: \"cubic-bezier(0.4, 0, 0.6, 1)\"\n        }\n    }\n};\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(themeOptions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/theme/theme.ts\n");

/***/ }),

/***/ "(rsc)/./app/components/AnimatedBackground.tsx":
/*!***********************************************!*\
  !*** ./app/components/AnimatedBackground.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\components\AnimatedBackground.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./app/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\Chat\app\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/ThemeProvider */ \"(rsc)/./app/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_AnimatedBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/AnimatedBackground */ \"(rsc)/./app/components/AnimatedBackground.tsx\");\n\n\n\nconst metadata = {\n    title: \"Ollama MCP Chat\",\n    description: \"A dynamic chat interface for Ollama LLM with integrated Smithery.ai MCP tool servers\",\n    keywords: [\n        \"ollama\",\n        \"mcp\",\n        \"chat\",\n        \"ai\",\n        \"llm\",\n        \"smithery\"\n    ],\n    authors: [\n        {\n            name: \"Ollama MCP Chat\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnimatedBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/framer-motion","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/hoist-non-react-statics","vendor-chunks/object-assign","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();