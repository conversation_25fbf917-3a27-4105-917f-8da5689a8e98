"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/route";
exports.ids = ["app/api/mcp/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/mcp/route.ts */ \"(rsc)/./app/api/mcp/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/route\",\n        pathname: \"/api/mcp\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\api\\\\mcp\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/mcp/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/mcp/route.ts":
/*!******************************!*\
  !*** ./app/api/mcp/route.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Store active MCP server processes\nconst activeServers = new Map();\n// GET /api/mcp - Get MCP configuration and available tools\nasync function GET() {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"public\", \"mcp.config.json\");\n        if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(configPath)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                mcpServers: {},\n                tools: []\n            });\n        }\n        const configData = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(configPath, \"utf8\");\n        const config = JSON.parse(configData);\n        // Get available tools from all configured servers\n        const tools = await getAvailableTools(config);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            mcpServers: config.mcpServers,\n            tools\n        });\n    } catch (error) {\n        console.error(\"Error reading MCP config:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to read MCP configuration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/mcp - Execute tool call or update configuration\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, serverName, toolName, arguments: toolArgs, config } = body;\n        switch(action){\n            case \"execute_tool\":\n                return await executeToolCall(serverName, toolName, toolArgs);\n            case \"update_config\":\n                return await updateMCPConfig(config);\n            case \"start_server\":\n                return await startMCPServer(serverName);\n            case \"stop_server\":\n                return await stopMCPServer(serverName);\n            default:\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in MCP API:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to process MCP request\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to get available tools from MCP servers\nasync function getAvailableTools(config) {\n    const tools = [];\n    // For demonstration, we'll define some common tools that the Exa server provides\n    // In a real implementation, you would query each server for its capabilities\n    if (config.mcpServers.exa) {\n        tools.push({\n            type: \"function\",\n            function: {\n                name: \"search_web\",\n                description: \"Search the web using Exa search engine\",\n                parameters: {\n                    type: \"object\",\n                    properties: {\n                        query: {\n                            type: \"string\",\n                            description: \"The search query to execute\"\n                        },\n                        num_results: {\n                            type: \"number\",\n                            description: \"Number of results to return (default: 10)\",\n                            default: 10\n                        },\n                        include_domains: {\n                            type: \"array\",\n                            items: {\n                                type: \"string\"\n                            },\n                            description: \"Domains to include in search\"\n                        },\n                        exclude_domains: {\n                            type: \"array\",\n                            items: {\n                                type: \"string\"\n                            },\n                            description: \"Domains to exclude from search\"\n                        }\n                    },\n                    required: [\n                        \"query\"\n                    ]\n                }\n            }\n        });\n    }\n    return tools;\n}\n// Execute a tool call on an MCP server\nasync function executeToolCall(serverName, toolName, toolArgs) {\n    try {\n        // For this implementation, we'll simulate tool execution\n        // In a real implementation, you would communicate with the actual MCP server\n        if (serverName === \"exa\" && toolName === \"search_web\") {\n            // Simulate web search results\n            const results = {\n                results: [\n                    {\n                        title: \"Example Search Result\",\n                        url: \"https://example.com\",\n                        snippet: \"This is a simulated search result for the query: \" + toolArgs.query\n                    }\n                ],\n                query: toolArgs.query,\n                num_results: toolArgs.num_results || 10\n            };\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                result: results\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: `Unknown tool: ${toolName} for server: ${serverName}`\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"Error executing tool call:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to execute tool call\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Update MCP configuration\nasync function updateMCPConfig(config) {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"public\", \"mcp.config.json\");\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(configPath, JSON.stringify(config, null, 2));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error updating MCP config:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to update configuration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Start an MCP server\nasync function startMCPServer(serverName) {\n    try {\n        // Implementation would start the actual MCP server process\n        // For now, we'll just simulate success\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: `Server ${serverName} started`\n        });\n    } catch (error) {\n        console.error(\"Error starting MCP server:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to start server\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Stop an MCP server\nasync function stopMCPServer(serverName) {\n    try {\n        const process1 = activeServers.get(serverName);\n        if (process1) {\n            process1.kill();\n            activeServers.delete(serverName);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: `Server ${serverName} stopped`\n        });\n    } catch (error) {\n        console.error(\"Error stopping MCP server:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to stop server\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/mcp/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();